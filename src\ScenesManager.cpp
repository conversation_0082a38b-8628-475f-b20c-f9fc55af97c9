#include "ScenesManager.h"
#include "PrinterManager.h"
#include "MacrosManager.h"
#include "EffectsManager.h"

/**
 * 构造函数
 */
ScenesManager::ScenesManager() {
    printerManager = nullptr;
    macrosManager = nullptr;
    effectsManager = nullptr;
    scenesEnabled = true;  // 默认启用场景功能
}

/**
 * 析构函数
 */
ScenesManager::~ScenesManager() {
    // 清理资源
}

/**
 * 初始化场景管理器
 */
void ScenesManager::begin() {
    Serial.println("初始化 ScenesManager...");
    
    // 从文件加载场景配置
    loadFromFile();
    
    Serial.println("ScenesManager 初始化完成");
}

/**
 * 主循环处理函数
 */
void ScenesManager::loop() {
    // 检查场景功能是否启用
    if (!scenesEnabled) {
        return;  // 场景功能被禁用，直接返回
    }

    // 进行必要的维护工作
    // 目前不需要定期处理任务
}

/**
 * 注册API端点到WebServer
 */
void ScenesManager::registerAPI(AsyncWebServer& server) {
    // 获取可用事件列表 - 必须放在通配符路由之前
    server.on("/api/scenes/events", HTTP_GET, [this](AsyncWebServerRequest* request) {
        this->handleGetAvailableEvents(request);
    });

    // 获取所有场景配置
    server.on("/api/scenes", HTTP_GET, [this](AsyncWebServerRequest* request) {
        this->handleGetScenes(request);
    });

    // 创建场景配置
    server.on("/api/scenes", HTTP_POST,
        [](AsyncWebServerRequest* request) {
            // 响应在onBody中处理
        },
        nullptr,
        [this](AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total) {
            this->handleCreateScene(request, data, len, index, total);
        }
    );

    // 更新场景配置 - 使用通配符路径
    server.on("/api/scenes/*", HTTP_PUT,
        [](AsyncWebServerRequest* request) {
            // 响应在onBody中处理
        },
        nullptr,
        [this](AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total) {
            this->handleUpdateScene(request, data, len, index, total);
        }
    );

    // 删除场景配置 - 使用通配符路径
    server.on("/api/scenes/*", HTTP_DELETE, [this](AsyncWebServerRequest* request) {
        this->handleDeleteScene(request);
    });
    
    Serial.println("ScenesManager API端点已注册");
}

/**
 * 获取所有场景配置
 */
const std::vector<SceneConfig>& ScenesManager::getScenes() const {
    return scenes;
}

/**
 * 根据ID获取场景配置
 */
const SceneConfig* ScenesManager::getSceneById(const String& id) const {
    for (const auto& scene : scenes) {
        if (scene.id == id) {
            return &scene;
        }
    }
    return nullptr;
}

/**
 * 添加新场景配置
 */
bool ScenesManager::addScene(const SceneConfig& scene) {
    // 验证场景配置
    if (!validateScene(scene)) {
        return false;
    }

    // 检查ID是否已存在
    if (getSceneById(scene.id) != nullptr) {
        return false;
    }
    
    // 添加场景
    scenes.push_back(scene);
    
    // 保存到文件
    if (saveToFile()) {
        Serial.printf("[Scenes] 添加成功: %s (ID: %s) - 事件: %s\n",
                     scene.name.c_str(), scene.id.c_str(), scene.eventName.c_str());
        return true;
    } else {
        // 保存失败，回滚
        scenes.pop_back();
        return false;
    }
}

/**
 * 更新场景配置
 */
bool ScenesManager::updateScene(const String& id, const SceneConfig& scene) {
    // 验证场景配置
    if (!validateScene(scene, id)) {
        return false;
    }
    
    // 查找要更新的场景
    for (auto& existingScene : scenes) {
        if (existingScene.id == id) {
            // 保存原始数据用于回滚
            SceneConfig originalScene = existingScene;
            
            // 更新场景数据
            existingScene = scene;
            existingScene.id = id; // 确保ID不变
            
            // 保存到文件
            if (saveToFile()) {
                Serial.printf("[Scenes] 更新成功: %s (ID: %s) - 事件: %s\n",
                             scene.name.c_str(), id.c_str(), scene.eventName.c_str());
                return true;
            } else {
                // 保存失败，回滚
                existingScene = originalScene;
                return false;
            }
        }
    }

    return false;
}

/**
 * 删除场景配置
 */
bool ScenesManager::deleteScene(const String& id) {
    // 查找要删除的场景
    for (auto it = scenes.begin(); it != scenes.end(); ++it) {
        if (it->id == id) {
            // 保存原始数据用于回滚
            SceneConfig deletedScene = *it;

            // 检查是否删除的是当前活动场景
            bool isActiveScene = (activeScene.sceneId == id);

            // 删除场景
            scenes.erase(it);

            // 保存到文件
            if (saveToFile()) {
                Serial.printf("[Scenes] 删除成功: %s (ID: %s)\n",
                             deletedScene.name.c_str(), deletedScene.id.c_str());

                // 如果删除的是当前活动场景，需要清除活动状态并停止灯效
                if (isActiveScene) {
                    activeScene.sceneId = "";
                    activeScene.effectId = "";
                    activeScene.startTime = 0;
                    activeScene.isActive = false;

                    // 停止当前灯效
                    if (effectsManager) {
                        effectsManager->stopCurrentEffect();
                    }

                    // 通知PrinterManager推送状态更新
                    if (printerManager) {
                        printerManager->onSceneStatusChanged();
                    }

                    // 延迟重新评估场景，确保LED有时间完全熄灭
                    // 这样用户可以看到删除场景后灯带确实熄灭了
                    delay(100);  // 100ms延迟，让用户看到灯带熄灭效果

                    // 重新评估场景，看是否有其他场景应该激活
                    evaluateSceneChange();
                }

                return true;
            } else {
                // 保存失败，回滚
                scenes.push_back(deletedScene);
                return false;
            }
        }
    }

    return false;
}

/**
 * 获取当前活动场景
 */
const ActiveScene& ScenesManager::getActiveScene() const {
    return activeScene;
}

/**
 * 检查是否有活动场景
 */
bool ScenesManager::hasActiveScene() const {
    return activeScene.isActive && !activeScene.sceneId.isEmpty();
}

/**
 * 根据场景ID获取场景名称
 */
String ScenesManager::getSceneName(const String& sceneId) const {
    const SceneConfig* scene = getSceneById(sceneId);
    return scene ? scene->name : "";
}

/**
 * 生成唯一的场景ID
 */
String ScenesManager::generateSceneId() const {
    return generateUniqueId("scenes");
}

/**
 * 验证场景配置是否有效
 */
bool ScenesManager::validateScene(const SceneConfig& scene, const String& excludeId) const {
    // 检查必填字段
    if (scene.name.isEmpty() || scene.eventName.isEmpty() || scene.effectId.isEmpty()) {
        return false;
    }
    
    // 检查事件名称和类型的匹配
    if (scene.eventType == SceneEventType::SYSTEM) {
        std::vector<String> systemEvents = getAvailableSystemEvents();
        bool found = false;
        for (const String& event : systemEvents) {
            if (event == scene.eventName) {
                found = true;
                break;
            }
        }
        if (!found) {
            return false;
        }
    }
    
    // 检查是否存在相同事件的场景（同一事件只能有一个场景）
    for (const auto& existingScene : scenes) {
        if (existingScene.id != excludeId && 
            existingScene.eventType == scene.eventType && 
            existingScene.eventName == scene.eventName) {
            return false;
        }
    }
    
    return true;
}

/**
 * 设置PrinterManager引用
 */
void ScenesManager::setPrinterManager(PrinterManager* printerManager) {
    this->printerManager = printerManager;
}

/**
 * 设置MacrosManager引用
 */
void ScenesManager::setMacrosManager(MacrosManager* macrosManager) {
    this->macrosManager = macrosManager;
}

/**
 * 设置EffectsManager引用
 */
void ScenesManager::setEffectsManager(EffectsManager* effectsManager) {
    this->effectsManager = effectsManager;
}

/**
 * 进行初始场景评估
 */
void ScenesManager::performInitialEvaluation() {
    // 确保所有管理器引用都已设置
    if (!printerManager) {
        return;
    }

    if (!effectsManager) {
        return;
    }

    // ESP32启动时，首先检查是否有espstart场景
    for (const auto& scene : scenes) {
        if (scene.eventType == SceneEventType::SYSTEM && scene.eventName == "espstart") {
            switchToScene(scene.id);
            return;
        }
    }
}

/**
 * 启用场景功能
 */
void ScenesManager::enableScenes() {
    scenesEnabled = true;

    // 立即评估当前状态，激活对应场景
    evaluateSceneChange();
}

/**
 * 禁用场景功能
 */
void ScenesManager::disableScenes() {
    scenesEnabled = false;
}

/**
 * 检查场景功能是否启用
 */
bool ScenesManager::isScenesEnabled() const {
    return scenesEnabled;
}

/**
 * 强制重新激活当前场景
 */
void ScenesManager::forceReactivateCurrentScene() {
    if (!scenesEnabled) {
        return;
    }

    if (activeScene.sceneId.isEmpty() || !activeScene.isActive) {
        return;
    }

    // 重新激活当前场景（强制执行，即使场景ID相同）
    String currentSceneId = activeScene.sceneId;

    // 临时清空活动场景，这样switchToScene会强制重新激活
    activeScene.sceneId = "";
    activeScene.isActive = false;

    // 重新切换到当前场景，这会重新执行灯效
    switchToScene(currentSceneId);
}

/**
 * 打印机状态变化回调函数
 */
void ScenesManager::onPrinterStatusChanged(const PrinterStatus& newStatus, const PrinterStatus& oldStatus) {
    // 检查场景功能是否启用
    if (!scenesEnabled) {
        return;
    }

    // 将新旧状态映射为事件
    String newEvent = mapPrinterStatusToEvent(newStatus);
    String oldEvent = mapPrinterStatusToEvent(oldStatus);

    // 特殊处理：如果当前活动场景是espstart，且接收到真实的打印机数据，则强制切换
    if (activeScene.sceneId != "" && activeScene.isActive) {
        const SceneConfig* currentScene = getSceneById(activeScene.sceneId);
        if (currentScene && currentScene->eventType == SceneEventType::SYSTEM &&
            currentScene->eventName == "espstart") {
            evaluateSceneChangeWithStatus(newStatus);
            return;
        }
    }

    // 只有当事件真正改变时才重新评估场景
    if (newEvent != oldEvent) {
        evaluateSceneChangeWithStatus(newStatus);
    }
}

/**
 * 宏状态变化回调函数
 */
void ScenesManager::onMacroStatusChanged(const String& macroName, int newStatus, int oldStatus) {
    // 检查场景功能是否启用
    if (!scenesEnabled) {
        return;
    }

    // 宏状态变化总是重新评估场景（因为可能影响优先级）
    evaluateSceneChange();
}

/**
 * 评估场景变化并切换
 */
void ScenesManager::evaluateSceneChange() {
    // 检查场景功能是否启用
    if (!scenesEnabled) {
        return;
    }

    // 获取当前打印机状态并进行评估
    if (printerManager) {
        PrinterStatus currentStatus = printerManager->getCurrentStatus();
        evaluateSceneChangeWithStatus(currentStatus);
    } else {
        // 如果没有PrinterManager，只能检查宏事件
        String newSceneId = determineActiveScene();
        if (newSceneId != activeScene.sceneId) {
            switchToScene(newSceneId);
        }
    }
}

/**
 * 带打印机状态的场景评估
 */
void ScenesManager::evaluateSceneChangeWithStatus(const PrinterStatus& currentStatus) {
    std::vector<SceneConfig> candidateScenes;

    // 1. 检查宏事件场景（优先级更高）
    if (macrosManager) {
        auto macros = macrosManager->getMacros();
        for (const auto& macro : macros) {
            if (macro.currentStatus == MacroStatus::RUNNING) {
                for (const auto& scene : scenes) {
                    if (scene.eventType == SceneEventType::MACRO &&
                        scene.eventName == macro.variableName) {
                        candidateScenes.push_back(scene);
                    }
                }
            }
        }
    }

    // 2. 如果有宏事件场景，优先使用宏事件场景
    if (!candidateScenes.empty()) {
        String newSceneId = candidateScenes[0].id;
        if (newSceneId != activeScene.sceneId) {
            switchToScene(newSceneId);
        }
        return;
    }

    // 3. 检查系统事件场景
    String currentEvent = mapPrinterStatusToEvent(currentStatus);

    for (const auto& scene : scenes) {
        if (scene.eventType == SceneEventType::SYSTEM &&
            scene.eventName == currentEvent) {
            candidateScenes.push_back(scene);
        }
    }

    // 4. 切换到系统事件场景或清除场景
    String newSceneId = candidateScenes.empty() ? "" : candidateScenes[0].id;

    if (newSceneId != activeScene.sceneId) {
        switchToScene(newSceneId);
    }
}

/**
 * 确定应该激活的场景
 */
String ScenesManager::determineActiveScene() {
    if (!printerManager || !macrosManager) {
        return "";
    }

    std::vector<SceneConfig> candidateScenes;

    // 1. 检查宏事件场景（优先级更高）
    auto macros = macrosManager->getMacros();
    for (const auto& macro : macros) {
        if (macro.currentStatus == MacroStatus::RUNNING) {
            for (const auto& scene : scenes) {
                if (scene.eventType == SceneEventType::MACRO &&
                    scene.eventName == macro.variableName) {
                    candidateScenes.push_back(scene);
                }
            }
        }
    }

    // 2. 如果有宏事件场景，优先返回宏事件场景
    if (!candidateScenes.empty()) {
        return candidateScenes[0].id;
    }

    // 3. 检查系统事件场景（通过当前存储的状态）
    // 注意：这里我们无法直接获取当前状态，但可以通过最近的状态变化来推断
    // 实际上，这个方法主要在状态变化时被调用，所以我们需要重新设计

    // 4. 没有匹配场景，返回空字符串
    return "";
}

/**
 * 切换到指定场景
 */
void ScenesManager::switchToScene(const String& sceneId) {
    const SceneConfig* scene = getSceneById(sceneId);

    if (scene) {
        // 更新活动场景
        activeScene.sceneId = sceneId;
        activeScene.effectId = scene->effectId;
        activeScene.startTime = millis();
        activeScene.isActive = true;

        // 通知PrinterManager推送场景状态更新
        if (printerManager) {
            printerManager->onSceneStatusChanged();
        }

        // 执行场景关联的灯效
        if (effectsManager && !scene->effectId.isEmpty()) {
            effectsManager->executePreset(scene->effectId);
        }
    } else {
        // 清除活动场景
        activeScene.sceneId = "";
        activeScene.effectId = "";
        activeScene.startTime = 0;
        activeScene.isActive = false;

        // 停止当前灯效
        if (effectsManager) {
            effectsManager->stopCurrentEffect();
        }

        // 通知PrinterManager推送场景状态更新
        if (printerManager) {
            printerManager->onSceneStatusChanged();
        }
    }
}

/**
 * 将打印机状态映射为事件名称
 */
String ScenesManager::mapPrinterStatusToEvent(const PrinterStatus& status) {
    // 根据打印机状态映射到事件名称
    if (status.state == "printing") return "printing";
    if (status.state == "paused") return "paused";
    if (status.state == "complete") return "completed";
    if (status.state == "error") return "error";

    // 检查是否在加热（有目标温度就是在加热）
    if (status.extruderTarget > 0 || status.bedTarget > 0) {
        return "heating";
    }

    // standby状态直接映射为standby事件
    if (status.state == "standby") return "standby";

    return "standby";  // 默认待机状态
}

/**
 * 获取可用的系统事件列表
 */
std::vector<String> ScenesManager::getAvailableSystemEvents() const {
    return {
        "espstart",   // ESP32启动状态
        "standby",    // 待机状态（对应打印机standby）
        "heating",    // 加热中
        "printing",   // 打印中
        "paused",     // 打印暂停
        "completed",  // 打印完成
        "error"       // 错误状态
    };
}

/**
 * 获取可用的宏事件列表
 */
std::vector<String> ScenesManager::getAvailableMacroEvents() const {
    std::vector<String> macroEvents;

    if (macrosManager) {
        auto macros = macrosManager->getMacros();
        for (const auto& macro : macros) {
            macroEvents.push_back(macro.variableName);
        }
    }

    return macroEvents;
}

/**
 * 保存场景配置到文件
 */
bool ScenesManager::saveToFile() {
    StaticJsonDocument<JSON_SCENES_BUFFER_SIZE> doc;
    JsonArray scenesArray = doc.createNestedArray("scenes");

    for (const auto& scene : scenes) {
        JsonObject sceneObj = scenesArray.createNestedObject();
        scene.toJson(sceneObj);
    }

    File file = LittleFS.open(configFile, "w");
    if (!file) {
        return false;
    }

    size_t bytesWritten = serializeJson(doc, file);
    file.close();

    if (bytesWritten == 0) {
        return false;
    }

    return true;
}

/**
 * 从文件加载场景配置
 */
bool ScenesManager::loadFromFile() {
    if (!LittleFS.exists(configFile)) {
        return false;
    }

    File file = LittleFS.open(configFile, "r");
    if (!file) {
        return false;
    }

    StaticJsonDocument<JSON_SCENES_BUFFER_SIZE> doc;
    DeserializationError error = deserializeJson(doc, file);
    file.close();

    if (error) {
        return false;
    }

    // 清空现有场景
    scenes.clear();

    // 加载场景配置
    JsonArray scenesArray = doc["scenes"];
    for (JsonObject sceneObj : scenesArray) {
        SceneConfig scene = SceneConfig::fromJson(sceneObj);
        scenes.push_back(scene);
    }

    return true;
}

/**
 * 发送JSON响应
 */
void ScenesManager::sendJsonResponse(AsyncWebServerRequest* request, int code, const String& message, const JsonDocument* data) {
    StaticJsonDocument<JSON_RESPONSE_BUFFER_SIZE> responseDoc;
    responseDoc["success"] = (code >= 200 && code < 300);
    responseDoc["message"] = message;
    responseDoc["timestamp"] = millis();

    if (data != nullptr && !data->isNull()) {
        responseDoc["data"] = *data;
    }

    String response;
    serializeJson(responseDoc, response);

    request->send(code, "application/json", response);
}

/**
 * 发送错误响应
 */
void ScenesManager::sendErrorResponse(AsyncWebServerRequest* request, int code, const String& message) {
    sendJsonResponse(request, code, message);
}

/**
 * 处理获取场景列表请求
 */
void ScenesManager::handleGetScenes(AsyncWebServerRequest* request) {
    StaticJsonDocument<JSON_SCENES_BUFFER_SIZE> dataDoc;
    JsonArray scenesArray = dataDoc.createNestedArray("scenes");

    for (const auto& scene : scenes) {
        JsonObject sceneObj = scenesArray.createNestedObject();
        scene.toJson(sceneObj);
    }

    // 添加活动场景信息
    JsonObject activeSceneObj = dataDoc.createNestedObject("activeScene");
    activeSceneObj["sceneId"] = activeScene.sceneId;
    activeSceneObj["effectId"] = activeScene.effectId;
    activeSceneObj["startTime"] = activeScene.startTime;
    activeSceneObj["isActive"] = activeScene.isActive;

    sendJsonResponse(request, HTTP_OK, "获取场景列表成功", &dataDoc);
}

/**
 * 处理创建场景请求
 */
void ScenesManager::handleCreateScene(AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total) {
    if (index + len == total) {
        StaticJsonDocument<JSON_SCENES_BUFFER_SIZE> doc;
        DeserializationError error = deserializeJson(doc, (char*)data, len);

        if (error) {
            sendErrorResponse(request, HTTP_BAD_REQUEST, "JSON解析失败: " + String(error.c_str()));
            return;
        }

        // 创建场景配置
        SceneConfig scene;
        scene.id = generateSceneId();
        scene.name = doc["name"].as<String>();
        scene.eventType = SceneConfig::stringToEventType(doc["eventType"].as<String>());
        scene.eventName = doc["eventName"].as<String>();
        scene.effectId = doc["effectId"].as<String>();
        scene.lastTriggered = 0;

        // 添加场景
        if (addScene(scene)) {
            StaticJsonDocument<JSON_SCENES_BUFFER_SIZE> responseData;
            JsonObject sceneObj = responseData.createNestedObject("scene");
            scene.toJson(sceneObj);

            // 立即评估场景变化，检查新场景是否应该激活
            evaluateSceneChange();

            sendJsonResponse(request, HTTP_OK, "场景创建成功", &responseData);
        } else {
            sendErrorResponse(request, HTTP_BAD_REQUEST, "场景创建失败");
        }
    }
}

/**
 * 处理更新场景请求
 */
void ScenesManager::handleUpdateScene(AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total) {
    if (index + len == total) {
        // 从URL中提取场景ID - 手动解析路径
        String url = request->url();
        int lastSlash = url.lastIndexOf('/');
        String sceneId = url.substring(lastSlash + 1);

        StaticJsonDocument<JSON_SCENES_BUFFER_SIZE> doc;
        DeserializationError error = deserializeJson(doc, (char*)data, len);

        if (error) {
            sendErrorResponse(request, HTTP_BAD_REQUEST, "JSON解析失败: " + String(error.c_str()));
            return;
        }

        // 创建更新的场景配置
        SceneConfig scene;
        scene.id = sceneId;
        scene.name = doc["name"].as<String>();
        scene.eventType = SceneConfig::stringToEventType(doc["eventType"].as<String>());
        scene.eventName = doc["eventName"].as<String>();
        scene.effectId = doc["effectId"].as<String>();
        scene.lastTriggered = doc["lastTriggered"].as<unsigned long>();

        // 更新场景
        if (updateScene(sceneId, scene)) {
            StaticJsonDocument<JSON_SCENES_BUFFER_SIZE> responseData;
            JsonObject sceneObj = responseData.createNestedObject("scene");
            scene.toJson(sceneObj);

            // 立即评估场景变化，检查更新后的场景是否应该激活
            evaluateSceneChange();

            sendJsonResponse(request, HTTP_OK, "场景更新成功", &responseData);
        } else {
            sendErrorResponse(request, HTTP_BAD_REQUEST, "场景更新失败");
        }
    }
}

/**
 * 处理删除场景请求
 */
void ScenesManager::handleDeleteScene(AsyncWebServerRequest* request) {
    // 从URL中提取场景ID - 手动解析路径
    String url = request->url();
    int lastSlash = url.lastIndexOf('/');
    String sceneId = url.substring(lastSlash + 1);

    if (deleteScene(sceneId)) {
        sendJsonResponse(request, HTTP_OK, "场景删除成功");
    } else {
        sendErrorResponse(request, HTTP_NOT_FOUND, "场景不存在或删除失败");
    }
}

/**
 * 处理获取可用事件列表请求
 */
void ScenesManager::handleGetAvailableEvents(AsyncWebServerRequest* request) {
    StaticJsonDocument<JSON_SCENES_BUFFER_SIZE> dataDoc;

    // 系统事件
    JsonArray systemEventsArray = dataDoc.createNestedArray("systemEvents");
    auto systemEvents = getAvailableSystemEvents();
    for (const String& event : systemEvents) {
        systemEventsArray.add(event);
    }

    // 宏事件 - 返回详细信息
    JsonArray macroEventsArray = dataDoc.createNestedArray("macroEvents");
    if (macrosManager) {
        auto macros = macrosManager->getMacros();
        for (const auto& macro : macros) {
            JsonObject macroObj = macroEventsArray.createNestedObject();
            macroObj["variableName"] = macro.variableName;
            macroObj["displayName"] = macro.displayName;
        }
    }

    sendJsonResponse(request, HTTP_OK, "获取可用事件列表成功", &dataDoc);
}

/* 场景预设模块样式 */

/* 场景模块卡片头部布局 */
#scenes-module .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

/* 添加按钮 */
.add-scene-btn {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: #007bff;
    color: white;
    border: none;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    font-weight: bold;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0, 123, 255, 0.3);
    flex-shrink: 0;
}

.add-scene-btn:hover {
    background: #0056b3;
    box-shadow: 0 4px 8px rgba(0, 123, 255, 0.4);
}

/* 场景模块模态框样式 */
.module-modal-scenes {
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.2s ease, visibility 0.2s ease;
}

.module-modal-scenes.show {
    opacity: 1;
    visibility: visible;
}

.module-modal-scenes-content {
    background: white;
    border-radius: 12px;
    width: 90%;
    max-width: 500px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    transform: scale(0.9);
    transition: transform 0.2s ease;
    will-change: transform;
}

.module-modal-scenes.show .module-modal-scenes-content {
    transform: scale(1);
}

.module-modal-scenes-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 24px;
    border-bottom: 1px solid #e9ecef;
}

.module-modal-scenes-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: #495057;
    margin: 0;
}

.module-modal-scenes-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: #6c757d;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.2s ease;
}

.module-modal-scenes-close:hover {
    background: #f8f9fa;
    color: #495057;
}

.module-modal-scenes-body {
    padding: 20px 24px;
}

.module-modal-scenes-footer {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    gap: 12px;
    padding: 20px 24px;
    border-top: 1px solid #e9ecef;
    background: #f8f9fa;
    border-radius: 0 0 12px 12px;
    min-height: 60px;
}

/* 表单样式 */
.scenes-form-group {
    margin-bottom: 20px;
}

.scenes-form-label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #495057;
    font-size: 0.9rem;
}

.scenes-form-label span {
    color: #dc3545;
}

.scenes-form-control {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #ced4da;
    border-radius: 6px;
    font-size: 0.9rem;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    box-sizing: border-box;
}

.scenes-form-control:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.scenes-form-control:disabled {
    background-color: #e9ecef;
    color: #6c757d;
}

.scenes-form-control.error {
    border-color: #dc3545;
    box-shadow: 0 0 0 2px rgba(220, 53, 69, 0.25);
}

/* 美化下拉框样式 */
.scenes-form-control select,
select.scenes-form-control {
    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 12px center;
    background-size: 16px;
    padding-right: 40px;
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
}

/* 下拉框选项组样式 */
.scenes-form-control optgroup {
    font-weight: bold;
    color: #495057;
    background-color: #f8f9fa;
    padding: 8px 12px 4px 12px;
    font-size: 0.85rem;
    border-bottom: 1px solid #e9ecef;
}

.scenes-form-control option {
    padding: 8px 12px;
    color: #495057;
    background-color: white;
    font-weight: normal;
}

.scenes-form-control option:hover {
    background-color: #f8f9fa;
}

/* 选中状态 */
.scenes-form-control option:checked {
    background-color: #007bff;
    color: white;
}

.scenes-form-hint {
    font-size: 0.8rem;
    color: #6c757d;
    margin-top: 4px;
}

.scenes-form-error {
    font-size: 0.8rem;
    color: #dc3545;
    margin-top: 4px;
    display: none;
}

.scenes-form-error.show {
    display: block;
}

/* 按钮样式 */
.module-modal-scenes .scenes-btn {
    padding: 8px 16px;
    border: none;
    border-radius: 6px;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
    text-align: center;
    min-width: 80px;
    white-space: nowrap;
}

.scenes-btn-primary {
    background: #007bff;
    color: white;
}

.scenes-btn-primary:hover {
    background: #0056b3;
}

.scenes-btn-secondary {
    background: #6c757d;
    color: white;
}

.scenes-btn-secondary:hover {
    background: #545b62;
}

.module-modal-scenes .scenes-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
}

/* 场景列表样式 */
.scenes-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.scene-item {
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 12px;
    background: white;
    transition: all 0.2s ease;
}

.scene-item:hover {
    border-color: #007bff;
    box-shadow: 0 2px 8px rgba(0, 123, 255, 0.15);
}

.scene-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 6px;
}

.scene-info {
    display: flex;
    align-items: center;
    gap: 8px;
}

.scene-icon {
    font-size: 1rem;
}

.scene-name {
    font-weight: 600;
    color: #495057;
    font-size: 0.9rem;
}

.scene-actions {
    display: flex;
    gap: 8px;
}

.scene-edit-btn,
.scene-delete-btn {
    padding: 4px 8px;
    font-size: 0.75rem;
    border-radius: 4px;
    border: none;
    cursor: pointer;
    transition: all 0.2s ease;
    font-weight: 500;
}

.scene-edit-btn {
    background: #ffc107;
    color: #212529;
}

.scene-edit-btn:hover {
    background: #e0a800;
}

.scene-delete-btn {
    background: #dc3545;
    color: white;
}

.scene-delete-btn:hover {
    background: #c82333;
}

.scene-details {
    display: flex;
    flex-direction: column;
    gap: 3px;
    font-size: 0.75rem;
    color: #6c757d;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .module-modal-scenes-content {
        width: 95%;
        margin: 10px;
    }

    .module-modal-scenes-header,
    .module-modal-scenes-body,
    .module-modal-scenes-footer {
        padding: 15px 20px;
    }

    .module-modal-scenes-footer {
        padding: 15px 20px;
        gap: 10px;
    }

    .module-modal-scenes .scenes-btn {
        min-width: 70px;
        padding: 8px 12px;
    }
}

.scene-trigger,
.scene-effect {
    display: flex;
    align-items: center;
}

/* 空状态样式 */
.empty-state {
    text-align: center;
    padding: 40px 20px;
    color: #6c757d;
}

.empty-state-icon {
    font-size: 3rem;
    margin-bottom: 16px;
    opacity: 0.6;
}

.empty-state-text {
    font-size: 1.1rem;
    font-weight: 500;
    margin-bottom: 8px;
    color: #495057;
}

.empty-state-hint {
    font-size: 0.9rem;
    opacity: 0.8;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .module-modal-scenes-content {
        width: 95%;
        margin: 20px;
    }
    
    .module-modal-scenes-header {
        padding: 15px 20px;
    }
    
    .module-modal-scenes-body {
        padding: 15px 20px;
    }
}

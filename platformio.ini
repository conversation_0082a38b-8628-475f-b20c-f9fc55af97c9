; PlatformIO Project Configuration File
;
;   Build options: build flags, source filter
;   Upload options: custom upload port, speed and extra flags
;   Library options: dependencies, extra library storages
;   Advanced options: extra scripting
;
; Please visit documentation for the other options and examples
; https://docs.platformio.org/page/projectconf.html

[env:esp32dev]
platform = espressif32
board = esp32dev
framework = arduino
monitor_speed = 115200
build_flags = -I include
lib_deps =
	bblanchon/Arduino<PERSON>son@^6.21.3
	gilmaimon/ArduinoWebsockets@^0.5.3
	esphome/AsyncTCP-esphome@^2.0.0
	esphome/ESPAsyncWebServer-esphome@^3.1.0
	fastled/FastLED@^3.6.0

; 文件系统配置
board_build.filesystem = littlefs
board_build.partitions = default.csv
; 为LittleFS分配更多空间 (可选)
; board_build.partitions = huge_app.csvima
/* 宏管理模块专用样式 */

/* ==================== 页面布局 ==================== */

.macros-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

.macros-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 2px solid #e9ecef;
}

.macros-header h1 {
    margin: 0;
    font-size: 1.8rem;
    color: #333;
    font-weight: 600;
}

/* 头部按钮容器 */
.header-buttons {
    display: flex;
    gap: 8px;
    align-items: center;
}

/* 帮助按钮 */
.macro-help-btn {
    width: 32px;
    height: 32px;
    border-radius: 8px;
    background: linear-gradient(135deg, #17a2b8, #138496);
    color: white;
    border: none;
    font-size: 1.1rem;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    box-shadow: 0 2px 6px rgba(23, 162, 184, 0.3);
    flex-shrink: 0;
    position: relative;
    overflow: hidden;
}

.macro-help-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.macro-help-btn:hover {
    background: linear-gradient(135deg, #138496, #117a8b);
    box-shadow: 0 4px 12px rgba(23, 162, 184, 0.4);
    transform: translateY(-1px);
}

.macro-help-btn:hover::before {
    left: 100%;
}

.macro-help-btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 6px rgba(23, 162, 184, 0.3);
}

.add-macro-btn {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: #007bff;
    color: white;
    border: none;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    font-weight: bold;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0, 123, 255, 0.3);
    flex-shrink: 0;
}

.add-macro-btn:hover {
    background: #0056b3;
    box-shadow: 0 4px 8px rgba(0, 123, 255, 0.4);
}



/* ==================== 宏列表区域 ==================== */

#macros-module .macros-list-header {
    display: none; /* 隐藏标题，保持与其他模块一致 */
}

#macros-module .macros-list {
    max-height: 300px;
    overflow-y: auto;
}

/* ==================== 宏配置项样式 ==================== */

#macros-module .macro-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background: #fff;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    margin-bottom: 8px;
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

#macros-module .macro-item:hover {
    background: #f8f9fa;
    border-color: #007bff;
    box-shadow: 0 2px 8px rgba(0, 123, 255, 0.15);
}

#macros-module .macro-item:last-child {
    margin-bottom: 0;
}

#macros-module .macro-info {
    flex: 1;
}

#macros-module .macro-name {
    font-size: 0.95rem;
    font-weight: 600;
    color: #495057;
    margin-bottom: 4px;
}

#macros-module .macro-variable {
    font-size: 0.8rem;
    color: #6c757d;
    font-family: 'Courier New', monospace;
}

#macros-module .macro-actions {
    display: flex;
    gap: 6px;
}

#macros-module .btn-sm {
    padding: 4px 8px;
    font-size: 0.75rem;
    border-radius: 4px;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
}

#macros-module .btn-edit {
    background: #17a2b8;
    color: white;
}

#macros-module .btn-edit:hover {
    background: #138496;
}

#macros-module .btn-delete {
    background: #dc3545;
    color: white;
}

#macros-module .btn-delete:hover {
    background: #c82333;
}

/* ==================== 空状态 ==================== */

#macros-module .macros-empty {
    text-align: center;
    padding: 40px 20px;
    color: #6c757d;
}

#macros-module .macros-empty-icon {
    font-size: 3rem;
    margin-bottom: 16px;
    opacity: 0.5;
}

#macros-module .macros-empty-text {
    font-size: 1rem;
    margin-bottom: 8px;
}

#macros-module .macros-empty-hint {
    font-size: 0.85rem;
    opacity: 0.8;
}

/* ==================== 模态框样式 ==================== */

.module-modal-macros {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    animation: fadeIn 0.3s ease;
}

.module-modal-macros.show {
    display: flex;
    align-items: center;
    justify-content: center;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.module-modal-macros-content {
    background: white;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    width: 90%;
    max-width: 500px;
    max-height: 90vh;
    overflow: hidden;
    animation: slideIn 0.3s ease;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-50px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.module-modal-macros-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid #e9ecef;
    background: #f8f9fa;
}

.module-modal-macros-title {
    margin: 0;
    font-size: 1.3rem;
    color: #333;
    font-weight: 600;
}

.module-modal-macros-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: #666;
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    width: 35px;
    height: 35px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.module-modal-macros-close:hover {
    background: #e9ecef;
    color: #333;
}

.module-modal-macros-body {
    padding: 25px;
}

.module-modal-macros-footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    padding: 20px;
    border-top: 1px solid #e9ecef;
    background: #f8f9fa;
}

/* ==================== 表单样式 ==================== */

.macros-form-group {
    margin-bottom: 20px;
}

.macros-form-label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #495057;
    font-size: 0.9rem;
}

.macros-form-label span {
    color: #dc3545;
}

.macros-form-control {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #ced4da;
    border-radius: 6px;
    font-size: 0.9rem;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    box-sizing: border-box;
}

.macros-form-control:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}



.macros-form-control.error {
    border-color: #dc3545;
    box-shadow: 0 0 0 2px rgba(220, 53, 69, 0.25);
}

.macros-form-hint {
    font-size: 0.8rem;
    color: #6c757d;
    margin-top: 4px;
}

.macros-form-error {
    font-size: 0.8rem;
    color: #dc3545;
    margin-top: 4px;
    display: none;
}

.macros-form-error.show {
    display: block;
}

/* ==================== 按钮样式 ==================== */

.macros-btn {
    padding: 8px 16px;
    border: none;
    border-radius: 6px;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
    text-align: center;
    min-width: 80px;
}

.macros-btn-primary {
    background: #007bff;
    color: white;
}

.macros-btn-primary:hover {
    background: #0056b3;
}

.macros-btn-secondary {
    background: #6c757d;
    color: white;
}

.macros-btn-secondary:hover {
    background: #545b62;
}

.macros-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
}

/* ==================== 响应式设计 ==================== */

@media (max-width: 768px) {
    #macros-module .macros-stats {
        flex-direction: column;
        gap: 8px;
    }

    #macros-module .stat-card:not(:last-child) {
        border-right: none;
        border-bottom: 1px solid #dee2e6;
        margin-right: 0;
        padding-bottom: 8px;
    }

    #macros-module .macro-item {
        padding: 10px 12px;
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }

    #macros-module .macro-actions {
        align-self: flex-end;
    }

    .module-modal-macros-content {
        width: 95%;
        margin: 10px;
    }

    .module-modal-macros-body {
        padding: 20px;
    }
}

@media (max-width: 480px) {
    #macros-module .macro-item {
        padding: 8px 10px;
    }

    #macros-module .macro-actions {
        width: 100%;
        justify-content: center;
    }

    .macro-help-modal {
        width: 95%;
        max-width: none;
    }

    .code-block {
        padding: 12px;
    }

    .code-block pre {
        font-size: 12px;
    }
}

/* ==================== 帮助模态框样式 ==================== */

.macro-help-modal {
    max-width: 800px;
    width: 90%;
}

.help-section {
    margin-bottom: 24px;
}

.help-description {
    font-size: 16px;
    color: #333;
    margin-bottom: 12px;
    font-weight: 500;
}

.help-note {
    font-size: 14px;
    color: #666;
    margin-bottom: 12px;
    font-style: italic;
}

.code-block {
    background: #2d3748;
    border-radius: 8px;
    padding: 16px;
    margin: 12px 0;
    overflow-x: auto;
}

.code-block pre {
    margin: 0;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-size: 13px;
    line-height: 1.5;
    color: #e2e8f0;
    white-space: pre;
}

.code-block code {
    background: none;
    padding: 0;
    border-radius: 0;
    font-family: inherit;
    font-size: inherit;
    color: inherit;
}

/* 帮助模态框关闭按钮样式 */
.macro-help-modal .module-modal-macros-footer {
    text-align: center;
    padding: 20px;
    border-top: 1px solid #dee2e6;
}

.macro-help-modal .macros-btn {
    min-width: 80px;
    padding: 8px 16px;
    font-size: 0.9rem;
    border-radius: 6px;
    border: none;
    cursor: pointer;
    transition: all 0.2s ease;
}

.macro-help-modal .macros-btn-primary {
    background: #007bff;
    color: white;
}

.macro-help-modal .macros-btn-primary:hover {
    background: #0056b3;
}

/* 帮助模态框头部关闭按钮 */
.macro-help-modal .module-modal-macros-close {
    background: none;
    border: none;
    font-size: 24px;
    color: #6c757d;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.macro-help-modal .module-modal-macros-close:hover {
    background: #f8f9fa;
    color: #495057;
    transform: scale(1.1);
}

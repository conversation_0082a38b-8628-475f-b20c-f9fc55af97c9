/* 分段管理模块样式 */

/* 模态框中的统计信息区域 */
.module-modal-segments .segments-stats {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 8px;
    padding: 8px 0;
    margin-bottom: 16px;
}

.module-modal-segments .stat-item {
    display: flex;
    align-items: center;
    gap: 6px;
    flex: 1;
    justify-content: center;
    padding: 8px;
    background: #f8f9fa;
    border-radius: 6px;
    border: 1px solid #e9ecef;
}

.module-modal-segments .stat-label {
    font-size: 0.85rem;
    color: #6c757d;
    font-weight: 500;
}

.module-modal-segments .stat-value {
    font-size: 0.9rem;
    font-weight: 700;
    color: #495057;
}

.module-modal-segments .stat-value.total {
    color: #007bff;
}

.module-modal-segments .stat-value.used {
    color: #28a745;
}

.module-modal-segments .stat-value.current {
    color: #ffc107;
}

.module-modal-segments .stat-value.available {
    color: #17a2b8;
}

/* 分段列表 */
.segments-list {
    max-height: 300px;
    overflow-y: auto;
}

.segment-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background: #fff;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    margin-bottom: 8px;
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.segment-item:hover {
    background: #f8f9fa;
    border-color: #007bff;
    box-shadow: 0 2px 8px rgba(0, 123, 255, 0.15);
}

.segment-item:last-child {
    margin-bottom: 0;
}

.segment-info {
    flex: 1;
}

.segment-name {
    font-weight: 600;
    color: #495057;
    margin-bottom: 4px;
    font-size: 0.95rem;
}

.segment-range {
    font-size: 0.8rem;
    color: #6c757d;
    font-family: 'Courier New', monospace;
}

.segment-actions {
    display: flex;
    gap: 8px;
}

.btn-sm {
    padding: 4px 8px;
    font-size: 0.75rem;
    border-radius: 4px;
    border: none;
    cursor: pointer;
    transition: all 0.2s ease;
    font-weight: 500;
}

.btn-edit {
    background: #ffc107;
    color: #212529;
}

.btn-edit:hover {
    background: #e0a800;
}

.btn-delete {
    background: #dc3545;
    color: white;
}

.btn-delete:hover {
    background: #c82333;
}

/* 分段模块卡片头部布局 */
#segments-module .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

/* 添加按钮 */
.add-segment-btn {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: #007bff;
    color: white;
    border: none;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    font-weight: bold;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0, 123, 255, 0.3);
    flex-shrink: 0;
}

.add-segment-btn:hover {
    background: #0056b3;
    box-shadow: 0 4px 8px rgba(0, 123, 255, 0.4);
}

/* 分段模块模态框样式 */
.module-modal-segments {
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.2s ease, visibility 0.2s ease;
}

.module-modal-segments.show {
    opacity: 1;
    visibility: visible;
}

.module-modal-segments-content {
    background: white;
    border-radius: 12px;
    width: 90%;
    max-width: 500px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    transform: scale(0.9);
    transition: transform 0.2s ease;
    will-change: transform;
}

.module-modal-segments.show .module-modal-segments-content {
    transform: scale(1);
}

.module-modal-segments-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 24px 0;
    border-bottom: 1px solid #e9ecef;
    margin-bottom: 20px;
}

.module-modal-segments-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: #495057;
    margin: 0;
}

.module-modal-segments-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: #6c757d;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.2s ease;
}

.module-modal-segments-close:hover {
    background: #f8f9fa;
    color: #495057;
}

.module-modal-segments-body {
    padding: 0 24px 20px;
}

.module-modal-segments-footer {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    gap: 12px;
    padding: 20px 24px;
    border-top: 1px solid #e9ecef;
    background: #f8f9fa;
    border-radius: 0 0 12px 12px;
    min-height: 60px;
}

/* 分段模块表单样式 */
.segments-form-group {
    margin-bottom: 20px;
}

.segments-form-label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #495057;
    font-size: 0.9rem;
}

.segments-form-label span {
    color: #dc3545;
}

.segments-form-control {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #ced4da;
    border-radius: 6px;
    font-size: 0.9rem;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    box-sizing: border-box;
}

.segments-form-control:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.segments-form-control:disabled {
    background-color: #e9ecef;
    color: #6c757d;
}

.segments-form-row {
    display: flex;
    gap: 12px;
}

.segments-form-col {
    flex: 1;
}

.segments-form-hint {
    font-size: 0.8rem;
    color: #6c757d;
    margin-top: 4px;
    font-style: italic;
}

/* 分段模块按钮样式 */
.module-modal-segments .segments-btn {
    padding: 8px 16px;
    border: none;
    border-radius: 6px;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
    text-align: center;
    min-width: 80px;
    white-space: nowrap;
}

.segments-btn-primary {
    background: #007bff;
    color: white;
}

.segments-btn-primary:hover {
    background: #0056b3;
}

.segments-btn-secondary {
    background: #6c757d;
    color: white;
}

.segments-btn-secondary:hover {
    background: #545b62;
}

.segments-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
}

/* 空状态 */
.empty-state {
    text-align: center;
    padding: 40px 20px;
    color: #6c757d;
}

.empty-state-icon {
    font-size: 3rem;
    margin-bottom: 16px;
    opacity: 0.5;
}

.empty-state-text {
    font-size: 1rem;
    margin-bottom: 8px;
}

.empty-state-hint {
    font-size: 0.85rem;
    opacity: 0.8;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .module-modal-segments .segments-stats {
        flex-wrap: wrap;
        gap: 6px;
    }

    .module-modal-segments .stat-item {
        flex: 1 1 calc(50% - 3px);
        min-width: calc(50% - 3px);
    }

    .segment-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }

    .segment-actions {
        align-self: flex-end;
    }

    .segments-form-row {
        flex-direction: column;
    }

    .module-modal-segments-footer {
        padding: 15px 20px;
        gap: 10px;
    }

    .segments-btn {
        min-width: 80px;
        padding: 8px 16px;
    }
}

#pragma once

// =================================================================
// 网络配置
// =================================================================

// WiFi配置
#define WIFI_SSID "CMCC-MFh6"
#define WIFI_PASSWORD "1284400514"

// Moonraker配置
#define MOONRAKER_HOST "*************"  // 替换为你的Moonraker服务器IP
#define MOONRAKER_PORT 7125             // Moonraker默认端口

// Web服务器配置
#define WEB_SERVER_PORT 80              // HTTP服务器端口

// =================================================================
// 硬件配置
// =================================================================

// LED配置
#define LED_COUNT 37                    // LED灯珠总数量
#define LED_PIN 13                      // LED数据引脚
#define LED_COLOR_ORDER GRB             // LED颜色顺序 (GRB/RGB/BGR等)
#define EFFECT_FPS_DEFAULT 120           // 默认帧率 (可设置30-120)

// LED默认参数
#define LED_BRIGHTNESS_DEFAULT 128      // 默认亮度 (1-255)
#define LED_SPEED_DEFAULT 50            // 默认速度 (1-100)
#define LED_INTENSITY_DEFAULT 80        // 默认强度 (1-100)

// 预览功能配置
#define PREVIEW_DEBOUNCE_DELAY 200      // 预览防抖延迟，毫秒

// =================================================================
// 系统常量
// =================================================================

// 调试和输出控制
#define ENABLE_SERIAL_STATUS_OUTPUT 0   // 启用串口状态输出 (1=启用, 0=禁用)

// 网络相关
#define RECONNECT_INTERVAL 5000         // WebSocket重连间隔，5秒
#define WIFI_CHECK_INTERVAL 30000       // WiFi检查间隔，30秒
#define MAX_RECONNECT_ATTEMPTS 10       // 最大重连尝试次数

// 错误处理
#define ERROR_RESET_INTERVAL 300000     // 错误重置间隔，5分钟
#define MAX_JSON_PARSE_ERRORS 10        // 最大JSON解析错误次数

// 内存管理 - JSON缓冲区大小
#define JSON_MESSAGE_BUFFER_SIZE 8192   // JSON消息缓冲区大小
#define JSON_SEND_BUFFER_SIZE 1024      // JSON发送缓冲区大小
#define JSON_STATUS_BUFFER_SIZE 1024    // 状态JSON缓冲区大小
#define JSON_SEGMENTS_BUFFER_SIZE 2048  // 分段JSON缓冲区大小
#define JSON_RESPONSE_BUFFER_SIZE 3072  // 响应JSON缓冲区大小
#define JSON_SCENES_BUFFER_SIZE 2048    // 场景JSON缓冲区大小
#define JSON_MACROS_BUFFER_SIZE 1536    // 宏事件JSON缓冲区大小
#define JSON_EFFECTS_BUFFER_SIZE 4096   // 灯效配置JSON缓冲区大小

// HTTP响应代码
#define HTTP_OK 200
#define HTTP_BAD_REQUEST 400
#define HTTP_NOT_FOUND 404
#define HTTP_INTERNAL_ERROR 500

// 错误代码定义
#define ERROR_SUCCESS 0
#define ERROR_JSON_PARSE 1001
#define ERROR_NETWORK_CONNECTION 1002
#define ERROR_FILE_IO 1003
#define ERROR_VALIDATION_FAILED 1004
#define ERROR_MEMORY_ALLOCATION 1005
#define ERROR_WEBSOCKET_CONNECTION 1006
#define ERROR_WIFI_CONNECTION 1007

// 宏状态变量配置
#define MACRO_STATUS_NAME "STATUS_VARIABLES"  // 默认宏名称

// =================================================================
// 通用工具函数
// =================================================================

/**
 * 生成唯一ID
 * 格式: prefix_timestamp_randomNumber
 * 示例: segments_12345678_5432, macros_12345679_7891, scenes_12345680_2468
 *
 * @param prefix ID前缀 (如: "segments", "macros", "scenes")
 * @return 唯一ID字符串
 */
inline String generateUniqueId(const String& prefix) {
    return prefix + "_" + String(millis()) + "_" + String(random(1000, 9999));
}
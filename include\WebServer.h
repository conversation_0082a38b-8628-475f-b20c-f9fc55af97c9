#pragma once

#include <Arduino.h>
#include <ESPAsyncWebServer.h>
#include <LittleFS.h>
#include "Config.h"

// 前向声明
class PrinterManager;
class SegmentsManager;
class EffectsManager;
class ScenesManager;
class MacrosManager;

/**
 * Web服务器类
 * 
 * 负责HTTP服务器管理、静态文件服务和模块API路由
 * 采用模块化设计，各功能模块独立注册API端点
 */
class WebServer {
public:
    /**
     * 构造函数
     * 
     * @param port HTTP服务器端口，默认80
     */
    WebServer(int port = WEB_SERVER_PORT);
    
    /**
     * 析构函数
     */
    ~WebServer();
    
    /**
     * 初始化Web服务器
     * 设置静态文件服务和基础路由
     */
    void begin();
    
    /**
     * 注册PrinterManager模块
     * 
     * @param printerManager PrinterManager实例指针
     */
    void registerPrinterManager(PrinterManager* printerManager);
    
    /**
     * 注册SegmentsManager模块
     * 
     * @param segmentsManager SegmentsManager实例指针
     */
    void registerSegmentsManager(SegmentsManager* segmentsManager);
    
    /**
     * 注册EffectsManager模块
     * 
     * @param effectsManager EffectsManager实例指针
     */
    void registerEffectsManager(EffectsManager* effectsManager);
    
    /**
     * 注册ScenesManager模块
     * 
     * @param scenesManager ScenesManager实例指针
     */
    void registerScenesManager(ScenesManager* scenesManager);
    
    /**
     * 注册MacrosManager模块
     *
     * @param macrosManager MacrosManager实例指针
     */
    void registerMacrosManager(MacrosManager* macrosManager);
    
    /**
     * 获取服务器状态信息
     * 
     * @return 服务器状态JSON字符串
     */
    String getServerInfo();
    
    /**
     * 检查服务器是否正在运行
     * 
     * @return 服务器运行状态
     */
    bool isRunning() const;

private:
    // AsyncWebServer实例
    AsyncWebServer server;
    
    // 服务器端口
    int serverPort;
    
    // 服务器运行状态
    bool running;
    
    // 模块指针（用于状态查询）
    PrinterManager* printer;
    SegmentsManager* segments;
    EffectsManager* effects;
    ScenesManager* scenes;
    MacrosManager* macros;
    
    /**
     * 设置静态文件服务
     * 配置LittleFS文件系统服务
     */
    void setupStaticFiles();
    
    /**
     * 设置基础API路由
     * 配置服务器信息、健康检查等基础端点
     */
    void setupBaseRoutes();
    
    /**
     * 设置CORS头
     * 允许跨域请求
     */
    void setupCORS();
    
    /**
     * 处理404错误
     */
    void handleNotFound(AsyncWebServerRequest* request);
    
    /**
     * 处理服务器信息请求
     */
    void handleServerInfo(AsyncWebServerRequest* request);
    
    /**
     * 处理健康检查请求
     */
    void handleHealthCheck(AsyncWebServerRequest* request);
    
    /**
     * 处理根路径请求
     */
    void handleRoot(AsyncWebServerRequest* request);
};

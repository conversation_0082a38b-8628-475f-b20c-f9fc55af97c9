// 分段管理模块

class SegmentsManager {
    constructor() {
        this.segments = [];
        this.stats = {
            totalLeds: 37,
            usedLeds: 0,
            availableLeds: 37,
            nextStartPosition: 1
        };
        this.currentEditingId = null;
        this.modalElements = null; // 缓存模态框DOM元素
        this.init();
    }

    // 初始化
    init() {
        console.log('初始化分段管理模块...');
        this.bindEvents();
        this.loadSegments();
    }

    // 绑定事件
    bindEvents() {
        // 添加分段按钮
        const addBtn = document.querySelector('#segments-module .add-segment-btn');
        if (addBtn) {
            addBtn.addEventListener('click', () => this.showAddModal());
        }

        // 模态框关闭事件 - 只通过关闭按钮
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('module-modal-segments-close')) {
                this.hideModal();
            }
        });

        // 取消按钮事件
        const cancelBtn = document.getElementById('segment-cancel-btn');
        if (cancelBtn) {
            cancelBtn.addEventListener('click', () => this.hideModal());
        }

        // 表单提交事件
        const form = document.getElementById('segment-form');
        if (form) {
            form.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleFormSubmit();
            });
        }

        // 实时计算灯珠数量和更新统计信息
        const startInput = document.getElementById('segment-start');
        const endInput = document.getElementById('segment-end');
        const lengthDisplay = document.getElementById('segment-length');

        if (startInput && endInput && lengthDisplay) {
            const updateLength = () => {
                const start = parseInt(startInput.value) || 0;
                const end = parseInt(endInput.value) || 0;
                const length = end >= start ? end - start + 1 : 0;
                lengthDisplay.textContent = length;

                // 同时更新模态框统计信息（使用优化后的方法）
                this.updateModalStats();
            };

            startInput.addEventListener('input', updateLength);
            endInput.addEventListener('input', updateLength);
        }
    }

    // 加载分段数据
    async loadSegments() {
        try {
            const response = await fetch('/api/segments');
            const data = await response.json();

            if (data.success) {
                this.segments = data.data.segments || [];
                this.stats = data.data.stats || this.stats;
                this.renderSegments();
                this.updateStats();
            } else {
                console.error('加载分段失败:', data.message);
                if (window.AppUtils && window.AppUtils.showError) {
                    window.AppUtils.showError('加载分段失败: ' + data.message);
                }
            }
        } catch (error) {
            console.error('加载分段出错:', error);
            if (window.AppUtils && window.AppUtils.showError) {
                window.AppUtils.showError('加载分段出错: ' + error.message);
            }
        }
    }

    // 渲染分段列表
    renderSegments() {
        const container = document.querySelector('#segments-module .card-content');
        if (!container) return;

        // 添加分段列表
        if (this.segments.length === 0) {
            container.innerHTML = `
                <div class="empty-state">
                    <div class="empty-state-icon">💡</div>
                    <div class="empty-state-text">暂无分段</div>
                    <div class="empty-state-hint">点击右上角的 + 号添加新分段</div>
                </div>
            `;
        } else {
            const listHtml = `
                <div class="segments-list">
                    ${this.segments.map(segment => this.renderSegmentItem(segment)).join('')}
                </div>
            `;
            container.innerHTML = listHtml;
        }

        // 重新绑定事件
        this.bindSegmentEvents();
    }

    // 渲染单个分段项
    renderSegmentItem(segment) {
        return `
            <div class="segment-item" data-id="${segment.id}">
                <div class="segment-info">
                    <div class="segment-name">${segment.name}</div>
                    <div class="segment-range">(${segment.startLed}-${segment.endLed}) - ${segment.length}个灯珠</div>
                </div>
                <div class="segment-actions">
                    <button class="btn-sm btn-edit" onclick="segmentsManager.editSegment('${segment.id}')">编辑</button>
                    <button class="btn-sm btn-delete" onclick="segmentsManager.deleteSegment('${segment.id}')">删除</button>
                </div>
            </div>
        `;
    }

    // 绑定分段项事件
    bindSegmentEvents() {
        // 事件已通过 onclick 属性绑定
    }

    // 更新统计信息
    updateStats() {
        // 统计信息在 renderSegments 中一起更新
    }

    // 获取缓存的模态框DOM元素
    getModalElements() {
        if (!this.modalElements) {
            this.modalElements = {
                startInput: document.getElementById('segment-start'),
                endInput: document.getElementById('segment-end'),
                totalElement: document.getElementById('modal-total-leds'),
                usedElement: document.getElementById('modal-used-leds'),
                currentElement: document.getElementById('modal-current-leds'),
                remainingElement: document.getElementById('modal-remaining-leds')
            };
        }
        return this.modalElements;
    }

    // 更新模态框中的统计信息
    updateModalStats() {
        // 使用缓存的DOM元素
        const elements = this.getModalElements();

        const startLed = parseInt(elements.startInput?.value) || 0;
        const endLed = parseInt(elements.endInput?.value) || 0;

        // 计算当前分段大小（未使用）
        const currentSegmentSize = (endLed >= startLed && startLed > 0) ? endLed - startLed + 1 : 0;

        // 计算已使用的灯珠数量（排除当前编辑的分段）
        let usedLeds = 0;
        if (this.currentEditingId) {
            // 编辑模式：排除当前编辑的分段
            usedLeds = this.segments
                .filter(segment => segment.id !== this.currentEditingId)
                .reduce((sum, segment) => sum + segment.length, 0);
        } else {
            // 添加模式：计算所有已保存的分段
            usedLeds = this.stats.usedLeds;
        }

        // 计算剩余灯珠数量
        const remainingLeds = this.stats.totalLeds - usedLeds - currentSegmentSize;

        // 更新显示（使用缓存的DOM元素）
        if (elements.totalElement) elements.totalElement.textContent = this.stats.totalLeds;
        if (elements.usedElement) elements.usedElement.textContent = usedLeds;
        if (elements.currentElement) elements.currentElement.textContent = currentSegmentSize;
        if (elements.remainingElement) {
            elements.remainingElement.textContent = remainingLeds;
            // 如果剩余数量为负数，显示为红色警告
            if (remainingLeds < 0) {
                elements.remainingElement.style.color = '#dc3545';
                elements.remainingElement.parentElement.style.borderColor = '#dc3545';
                elements.remainingElement.parentElement.style.backgroundColor = '#f8d7da';
            } else {
                elements.remainingElement.style.color = '#17a2b8';
                elements.remainingElement.parentElement.style.borderColor = '#e9ecef';
                elements.remainingElement.parentElement.style.backgroundColor = '#f8f9fa';
            }
        }
    }

    // 显示添加模态框
    showAddModal() {
        this.currentEditingId = null;
        this.showModal('添加新分段', {
            name: '',
            startLed: this.stats.nextStartPosition,
            endLed: Math.min(this.stats.nextStartPosition + 9, this.stats.totalLeds)
        });
    }

    // 编辑分段
    editSegment(id) {
        const segment = this.segments.find(s => s.id === id);
        if (segment) {
            this.currentEditingId = id;
            this.showModal('编辑分段', segment);
        }
    }

    // 删除分段
    async deleteSegment(id) {
        console.log('删除分段:', id);
        const segment = this.segments.find(s => s.id === id);
        if (!segment) {
            console.error('未找到分段:', id);
            return;
        }

        if (!confirm(`确定要删除分段 "${segment.name}" 吗？`)) {
            return;
        }

        try {
            console.log('发送删除请求:', `/api/segments/${id}`);
            const response = await fetch(`/api/segments/${id}`, {
                method: 'DELETE'
            });

            console.log('删除响应状态:', response.status);
            const data = await response.json();
            console.log('删除响应数据:', data);

            if (data.success) {
                console.log('删除成功');
                if (window.AppUtils && window.AppUtils.showSuccess) {
                    window.AppUtils.showSuccess('分段删除成功');
                } else {
                    alert('分段删除成功');
                }
                this.loadSegments(); // 重新加载数据

                // 发送分段变化事件通知其他模块
                document.dispatchEvent(new CustomEvent('segmentChanged', {
                    detail: {
                        action: 'deleted',
                        timestamp: Date.now()
                    }
                }));
            } else {
                console.error('删除失败:', data.message);
                if (window.AppUtils && window.AppUtils.showError) {
                    window.AppUtils.showError('删除失败: ' + data.message);
                } else {
                    alert('删除失败: ' + data.message);
                }
            }
        } catch (error) {
            console.error('删除分段出错:', error);
            if (window.AppUtils && window.AppUtils.showError) {
                window.AppUtils.showError('删除分段出错: ' + error.message);
            } else {
                alert('删除分段出错: ' + error.message);
            }
        }
    }

    // 显示模态框
    showModal(title, data) {
        const modal = document.getElementById('segment-modal');
        const titleElement = document.getElementById('modal-title');
        const form = document.getElementById('segment-form');

        if (!modal || !titleElement || !form) {
            console.error('模态框元素未找到');
            return;
        }

        // 设置标题
        titleElement.textContent = title;

        // 更新模态框中的统计信息
        this.updateModalStats();

        // 填充表单数据
        document.getElementById('segment-name').value = data.name || '';
        document.getElementById('segment-start').value = data.startLed || this.stats.nextStartPosition;
        document.getElementById('segment-end').value = data.endLed || Math.min((data.startLed || this.stats.nextStartPosition) + 9, this.stats.totalLeds);

        // 触发长度计算
        const event = new Event('input');
        document.getElementById('segment-end').dispatchEvent(event);

        // 显示模态框
        modal.classList.add('show');
    }

    // 隐藏模态框
    hideModal() {
        const modal = document.getElementById('segment-modal');
        if (modal) {
            modal.classList.remove('show');
        }
        // 清除DOM元素缓存，确保下次重新获取
        this.modalElements = null;
        this.currentEditingId = null;
    }

    // 处理表单提交
    async handleFormSubmit() {
        console.log('提交表单，当前编辑ID:', this.currentEditingId);

        const formData = {
            name: document.getElementById('segment-name').value.trim(),
            startLed: parseInt(document.getElementById('segment-start').value),
            endLed: parseInt(document.getElementById('segment-end').value)
        };

        console.log('表单数据:', formData);

        // 验证表单
        if (!this.validateForm(formData)) {
            return;
        }

        try {
            let response;
            if (this.currentEditingId) {
                // 更新分段
                console.log('发送更新请求:', `/api/segments/${this.currentEditingId}`);
                response = await fetch(`/api/segments/${this.currentEditingId}`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(formData)
                });
            } else {
                // 创建分段
                console.log('发送创建请求:', '/api/segments');
                response = await fetch('/api/segments', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(formData)
                });
            }

            console.log('响应状态:', response.status);
            const data = await response.json();
            console.log('响应数据:', data);

            if (data.success) {
                const message = this.currentEditingId ? '分段更新成功' : '分段创建成功';
                console.log(message);

                            if (window.AppUtils && window.AppUtils.showSuccess) {
                window.AppUtils.showSuccess(message);
                } else {
                    alert(message);
                }

                this.hideModal();
                this.loadSegments(); // 重新加载数据

                // 发送分段变化事件通知其他模块
                document.dispatchEvent(new CustomEvent('segmentChanged', {
                    detail: {
                        action: this.currentEditingId ? 'updated' : 'added',
                        timestamp: Date.now()
                    }
                }));
            } else {
                console.error('操作失败:', data.message);
                            if (window.AppUtils && window.AppUtils.showError) {
                window.AppUtils.showError('操作失败: ' + data.message);
                } else {
                    alert('操作失败: ' + data.message);
                }
            }
        } catch (error) {
            console.error('提交表单出错:', error);
                    if (window.AppUtils && window.AppUtils.showError) {
            window.AppUtils.showError('操作失败: ' + error.message);
            } else {
                alert('操作失败: ' + error.message);
            }
        }
    }

    // 验证表单
    validateForm(data) {
        if (!data.name) {
            if (window.AppUtils && window.AppUtils.showError) {
                window.AppUtils.showError('请输入分段名称');
            }
            return false;
        }

        if (data.startLed < 1 || data.startLed > this.stats.totalLeds) {
            if (window.AppUtils && window.AppUtils.showError) {
                window.AppUtils.showError(`起始位置必须在 1-${this.stats.totalLeds} 之间`);
            }
            return false;
        }

        if (data.endLed < 1 || data.endLed > this.stats.totalLeds) {
            if (window.AppUtils && window.AppUtils.showError) {
                window.AppUtils.showError(`结束位置必须在 1-${this.stats.totalLeds} 之间`);
            }
            return false;
        }

        if (data.startLed > data.endLed) {
            if (window.AppUtils && window.AppUtils.showError) {
                window.AppUtils.showError('起始位置不能大于结束位置');
            }
            return false;
        }

        return true;
    }
}

// 全局实例
let segmentsManager;

// 初始化
document.addEventListener('DOMContentLoaded', function() {
    segmentsManager = new SegmentsManager();
});

// 主脚本文件 - 全局功能

// 全局变量
let connectionStatus = 'disconnected';
let lastUpdateTime = null;

// DOM元素
const connectionIndicator = document.getElementById('connection-indicator');
const connectionText = document.getElementById('connection-text');
const lastUpdateElement = document.getElementById('last-update');

// 初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('ESP32 Moonraker Monitor 初始化...');
    updateConnectionStatus('connecting');

    // 注意：PrinterManager的初始化现在在HTML中的内联脚本中处理
    // 这里不再需要初始化PrinterManager，避免重复初始化
    console.log('主脚本初始化完成，PrinterManager将由页面加载事件处理');

    // 设置定时更新最后更新时间 - 从1秒优化到2秒，减少DOM操作频率
    setInterval(updateLastUpdateTime, 2000);
});

// 更新连接状态
function updateConnectionStatus(status) {
    connectionStatus = status;
    
    // 移除所有状态类
    connectionIndicator.classList.remove('connected', 'disconnected', 'connecting');
    
    // 添加当前状态类
    connectionIndicator.classList.add(status);
    
    // 更新状态文本
    switch(status) {
        case 'connected':
            connectionText.textContent = '已连接';
            break;
        case 'connecting':
            connectionText.textContent = '连接中...';
            break;
        case 'disconnected':
            connectionText.textContent = '未连接';
            break;
        default:
            connectionText.textContent = '未知状态';
    }
    
    console.log('连接状态更新:', status);
}

// 更新最后更新时间
function updateLastUpdateTime() {
    if (lastUpdateTime && lastUpdateElement) {
        const now = new Date();
        const diff = Math.floor((now - lastUpdateTime) / 1000);
        
        let timeText;
        if (diff < 60) {
            timeText = `${diff}秒前`;
        } else if (diff < 3600) {
            timeText = `${Math.floor(diff / 60)}分钟前`;
        } else {
            timeText = `${Math.floor(diff / 3600)}小时前`;
        }
        
        lastUpdateElement.textContent = `最后更新: ${timeText}`;
    }
}

// 设置最后更新时间
function setLastUpdateTime() {
    lastUpdateTime = new Date();
    if (lastUpdateElement) {
        lastUpdateElement.textContent = `最后更新: 刚刚`;
    }
}

// 显示错误消息
function showError(message) {
    console.error('错误:', message);
    // 这里可以添加用户友好的错误显示逻辑
}

// 显示成功消息
function showSuccess(message) {
    console.log('成功:', message);
    // 这里可以添加用户友好的成功显示逻辑
}

// 工具函数：格式化数字
function formatNumber(num, decimals = 1) {
    if (num === null || num === undefined || isNaN(num)) {
        return '--';
    }
    return Number(num).toFixed(decimals);
}

// 工具函数：格式化百分比
function formatPercentage(value) {
    if (value === null || value === undefined || isNaN(value)) {
        return '0%';
    }
    return `${Math.round(value * 100)}%`;
}

// 工具函数：安全设置元素文本
function safeSetText(elementId, text) {
    const element = document.getElementById(elementId);
    if (element) {
        element.textContent = text || '--';
    }
}

// 工具函数：安全设置元素HTML
function safeSetHTML(elementId, html) {
    const element = document.getElementById(elementId);
    if (element) {
        element.innerHTML = html || '';
    }
}

// 工具函数：安全添加CSS类
function safeAddClass(elementId, className) {
    const element = document.getElementById(elementId);
    if (element) {
        element.classList.add(className);
    }
}

// 工具函数：安全移除CSS类
function safeRemoveClass(elementId, className) {
    const element = document.getElementById(elementId);
    if (element) {
        element.classList.remove(className);
    }
}

// 工具函数：安全设置样式
function safeSetStyle(elementId, property, value) {
    const element = document.getElementById(elementId);
    if (element) {
        element.style[property] = value;
    }
}

// 导出全局函数供其他模块使用
window.AppUtils = {
    updateConnectionStatus,
    setLastUpdateTime,
    showError,
    showSuccess,
    formatNumber,
    formatPercentage,
    safeSetText,
    safeSetHTML,
    safeAddClass,
    safeRemoveClass,
    safeSetStyle
};

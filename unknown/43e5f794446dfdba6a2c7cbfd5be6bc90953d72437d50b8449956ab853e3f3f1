#include <Arduino.h>
#include <LittleFS.h>
#include <FastLED.h>
#include "Config.h"
#include "WiFiManager.h"
#include "MoonrakerClient.h"
#include "PrinterManager.h"
#include "SegmentsManager.h"
#include "MacrosManager.h"
#include "ScenesManager.h"
#include "WebServer.h"
#include "LightEffect.h"
#include "EffectsManager.h"

// =================================================================
// 全局变量
// =================================================================
WiFiManager wifiManager(WIFI_SSID, WIFI_PASSWORD, WIFI_CHECK_INTERVAL);
MoonrakerClient moonraker;
PrinterManager printerManager(moonraker);
SegmentsManager segmentsManager(LED_COUNT);
MacrosManager macrosManager(moonraker);
ScenesManager scenesManager;
EffectsManager effectsManager;
WebServer webServer(WEB_SERVER_PORT);

// =================================================================
// 函数声明
// =================================================================

/**
 * LED渲染任务 - 运行在Core 1
 * 专门处理LED灯效渲染，确保流畅的视觉效果
 */
void ledRenderTask(void* parameter) {
    TickType_t lastWakeTime = xTaskGetTickCount();

    Serial.println("LED渲染任务启动在Core 1");

    while(1) {
        // 处理LED灯效渲染
        effectsManager.loop();

        // 使用配置文件中的FPS设置
        vTaskDelayUntil(&lastWakeTime, pdMS_TO_TICKS(1000 / EFFECT_FPS_DEFAULT));
    }
}


// =================================================================
// 主程序入口
// =================================================================

/**
 * 初始化设置
 * 在启动时执行一次
 */
void setup() {
  // 初始化串口通信
  Serial.begin(115200);
  Serial.println("\n\n=========================");
  Serial.println("启动 ESP32 Moonraker 监控...");
  Serial.println("=========================");

  // 初始化LittleFS文件系统（必须在其他模块之前）
  if (!LittleFS.begin()) {
    Serial.println("LittleFS初始化失败!");
    Serial.println("系统无法启动，请检查文件系统!");
    while(1) {
      delay(1000); // 停止执行
    }
  }
  Serial.println("LittleFS初始化成功");

  // 连接到 WiFi
  wifiManager.connect();

  // 等待WiFi连接
  while (!wifiManager.isConnected()) {
    wifiManager.checkConnection();
    delay(100);
  }

  // 初始化 Moonraker 客户端
  moonraker.begin(MOONRAKER_HOST, MOONRAKER_PORT);

  // 初始化 PrinterManager
  printerManager.begin();

  // 设置状态变化回调：当MoonrakerClient收到数据时立即推送给前端
  moonraker.setStatusChangeCallback([]() {
    printerManager.onStatusChanged();
  });

  // 连接到Moonraker
  moonraker.connect();

  // 初始化 SegmentsManager
  segmentsManager.begin();

  // 初始化 MacrosManager
  macrosManager.begin();

  // 初始化 ScenesManager
  scenesManager.begin();

  // 初始化 EffectsManager
  effectsManager.begin();
  effectsManager.setSegmentsManager(&segmentsManager);
  effectsManager.setScenesManager(&scenesManager);
  effectsManager.setPrinterManager(&printerManager);

  // 建立模块间的关联
  printerManager.setMacrosManager(&macrosManager);
  printerManager.setScenesManager(&scenesManager);
  macrosManager.setPrinterManager(&printerManager);
  macrosManager.setScenesManager(&scenesManager);
  scenesManager.setPrinterManager(&printerManager);
  scenesManager.setMacrosManager(&macrosManager);
  scenesManager.setEffectsManager(&effectsManager);

  // 初始化 WebServer
  webServer.begin();
  webServer.registerPrinterManager(&printerManager);
  webServer.registerSegmentsManager(&segmentsManager);
  webServer.registerEffectsManager(&effectsManager);
  webServer.registerMacrosManager(&macrosManager);
  webServer.registerScenesManager(&scenesManager);

  Serial.println("=========================");
  Serial.println("系统初始化完成!");
  Serial.printf("Web服务器地址: http://%s\n", WiFi.localIP().toString().c_str());
  Serial.println("=========================");

  // 进行初始场景评估，检查是否有场景应该在启动时激活
  Serial.println("进行初始场景评估...");
  scenesManager.performInitialEvaluation();

  // 创建LED渲染任务在Core 1
  Serial.println("创建LED渲染任务...");
  xTaskCreatePinnedToCore(
    ledRenderTask,        // 任务函数
    "LEDRender",          // 任务名称
    4096,                 // 栈大小 (4KB)
    NULL,                 // 传递给任务的参数
    2,                    // 任务优先级 (高优先级)
    NULL,                 // 任务句柄
    1                     // 绑定到Core 1
  );

  Serial.println("多核心任务分配完成:");
  Serial.println("- Core 0: 网络通信、业务逻辑");
  Serial.println("- Core 1: LED渲染 (60fps)");
}

/**
 * 主循环 - 运行在Core 0
 * 处理网络通信和业务逻辑
 */
void loop() {
  // 检查WiFi连接状态
  wifiManager.checkConnection();

  // 处理 WebSocket 消息
  moonraker.poll();

  // 处理 PrinterManager 逻辑
  printerManager.loop();

  // 处理 MacrosManager 逻辑
  macrosManager.loop();

  // 处理 ScenesManager 逻辑
  scenesManager.loop();

  // LED渲染已移至Core 1的独立任务中
  // effectsManager.loop(); // 已移除

  // 降低CPU使用率 - 从1ms优化到10ms，大幅降低CPU负载
  delay(10);
}


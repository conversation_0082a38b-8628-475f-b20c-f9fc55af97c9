#pragma once

#include <Arduino.h>
#include <ArduinoJson.h>
#include <ESPAsyncWebServer.h>
#include <AsyncWebSocket.h>
#include "MoonrakerClient.h"
#include "Config.h"

// 前向声明
class MacrosManager;
class ScenesManager;

/**
 * 打印机状态数据结构
 * 用于缓存和比较状态变化
 */
struct PrinterStatus {
    String state;           // 打印机状态: standby, printing, paused, error等
    float progress;         // 打印进度 (0.0-1.0)
    String filename;        // 当前打印文件名
    float extruderTemp;     // 喷嘴当前温度
    float extruderTarget;   // 喷嘴目标温度
    float bedTemp;          // 热床当前温度
    float bedTarget;        // 热床目标温度
    unsigned long timestamp; // 状态更新时间戳
    
    // 构造函数
    PrinterStatus() {
        state = "standby";
        progress = 0.0;
        filename = "";
        extruderTemp = 0.0;
        extruderTarget = 0.0;
        bedTemp = 0.0;
        bedTarget = 0.0;
        timestamp = 0;
    }
    
    // 比较操作符，用于检测状态变化
    bool operator!=(const PrinterStatus& other) const {
        return (state != other.state ||
                abs(progress - other.progress) > 0.001 ||
                filename != other.filename ||
                abs(extruderTemp - other.extruderTemp) > 0.1 ||
                abs(extruderTarget - other.extruderTarget) > 0.1 ||
                abs(bedTemp - other.bedTemp) > 0.1 ||
                abs(bedTarget - other.bedTarget) > 0.1);
    }
};

/**
 * 打印机管理器类
 * 
 * 负责监控打印机状态变化并通过WebSocket实时推送给前端
 * 实现了观察者模式，监听MoonrakerClient的状态更新
 */
class PrinterManager {
public:
    /**
     * 构造函数
     * 
     * @param moonrakerClient MoonrakerClient实例的引用
     */
    PrinterManager(MoonrakerClient& moonrakerClient);
    
    /**
     * 析构函数
     */
    ~PrinterManager();
    
    /**
     * 初始化PrinterManager
     * 设置WebSocket服务器和状态监控
     */
    void begin();
    
    /**
     * 主循环处理函数
     * 检查状态变化并推送更新
     * 应在主循环中定期调用
     */
    void loop();
    
    /**
     * 注册API端点到WebServer
     * 
     * @param server AsyncWebServer实例的引用
     */
    void registerAPI(AsyncWebServer& server);
    
    /**
     * 获取当前打印机状态
     *
     * @return 当前状态的JSON字符串
     */
    String getCurrentStatusJSON();

    /**
     * 获取当前打印机状态对象
     *
     * @return 当前状态的PrinterStatus对象
     */
    const PrinterStatus& getCurrentStatus() const;
    
    /**
     * 检查是否有客户端连接
     *
     * @return 是否有WebSocket客户端连接
     */
    bool hasConnectedClients();

    /**
     * 状态变化回调函数
     * 当MoonrakerClient接收到新数据时被调用
     */
    void onStatusChanged();

    /**
     * 设置MacrosManager引用
     * 用于获取宏状态信息
     *
     * @param macrosManager MacrosManager实例指针
     */
    void setMacrosManager(MacrosManager* macrosManager);

    /**
     * 宏状态变化通知
     * 当MacrosManager检测到宏状态变化时被调用
     */
    void onMacroStatusChanged();

    /**
     * 设置ScenesManager引用
     * 用于获取场景状态信息
     *
     * @param scenesManager ScenesManager实例指针
     */
    void setScenesManager(ScenesManager* scenesManager);

    /**
     * 场景状态变化通知
     * 当ScenesManager检测到场景状态变化时被调用
     */
    void onSceneStatusChanged();

private:
    // MoonrakerClient引用
    MoonrakerClient& moonraker;

    // MacrosManager指针
    MacrosManager* macrosManager;

    // ScenesManager指针
    ScenesManager* scenesManager;

    // WebSocket服务器
    AsyncWebSocket ws;
    
    // 状态缓存
    PrinterStatus currentStatus;
    PrinterStatus lastStatus;
    
    // JSON文档，避免频繁内存分配
    StaticJsonDocument<JSON_STATUS_BUFFER_SIZE> statusDoc;
    
    /**
     * 从MoonrakerClient获取最新状态
     * 
     * @return 最新的打印机状态
     */
    PrinterStatus getStatusFromMoonraker();
    
    /**
     * 将状态转换为JSON格式
     * 
     * @param status 要转换的状态
     * @return JSON字符串
     */
    String statusToJSON(const PrinterStatus& status);
    
    /**
     * 向所有连接的WebSocket客户端广播状态更新
     * 
     * @param statusJSON 状态的JSON字符串
     */
    void broadcastStatus(const String& statusJSON);
    
    /**
     * WebSocket事件处理函数
     */
    void onWebSocketEvent(AsyncWebSocket* server, AsyncWebSocketClient* client, 
                         AwsEventType type, void* arg, uint8_t* data, size_t len);
    
    /**
     * 处理HTTP API请求 - 获取状态
     */
    void handleGetStatus(AsyncWebServerRequest* request);
    
    /**
     * 处理HTTP API请求 - 获取连接信息
     */
    void handleGetInfo(AsyncWebServerRequest* request);
    
    /**
     * 静态WebSocket事件处理函数（用于回调）
     */
    static void staticOnWebSocketEvent(AsyncWebSocket* server, AsyncWebSocketClient* client, 
                                      AwsEventType type, void* arg, uint8_t* data, size_t len);
    
    // 全局实例指针，用于静态回调
    static PrinterManager* instance;
};

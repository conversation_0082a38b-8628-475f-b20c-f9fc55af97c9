#pragma once

#include <Arduino.h>
#include <ArduinoJson.h>
#include <ESPAsyncWebServer.h>
#include <LittleFS.h>
#include <vector>
#include "Config.h"

// 前向声明
class PrinterManager;
class MacrosManager;
class EffectsManager;

/**
 * 场景事件类型枚举
 */
enum class SceneEventType {
    SYSTEM,     // 系统事件（打印机状态）
    MACRO       // 宏事件
};

/**
 * 场景配置数据结构
 */
struct SceneConfig {
    String id;                    // 场景唯一标识符
    String name;                  // 场景显示名称
    SceneEventType eventType;     // 事件类型
    String eventName;             // 事件名称（如"idle"、"homing"）
    String effectId;              // 关联的灯效ID
    unsigned long lastTriggered;  // 最后触发时间
    
    // 构造函数
    SceneConfig() : eventType(SceneEventType::SYSTEM), lastTriggered(0) {}
    SceneConfig(const String& sceneId, const String& sceneName, SceneEventType type, 
                const String& event, const String& effect)
        : id(sceneId), name(sceneName), eventType(type), eventName(event), 
          effectId(effect), lastTriggered(0) {}
    
    // 转换为JSON对象
    void toJson(JsonObject obj) const {
        obj["id"] = id;
        obj["name"] = name;
        obj["eventType"] = eventTypeToString(eventType);
        obj["eventName"] = eventName;
        obj["effectId"] = effectId;
        obj["lastTriggered"] = lastTriggered;
    }
    
    // 从JSON对象创建场景配置
    static SceneConfig fromJson(const JsonObject& obj) {
        SceneConfig scene;
        scene.id = obj["id"].as<String>();
        scene.name = obj["name"].as<String>();
        scene.eventType = stringToEventType(obj["eventType"].as<String>());
        scene.eventName = obj["eventName"].as<String>();
        scene.effectId = obj["effectId"].as<String>();
        scene.lastTriggered = obj["lastTriggered"].as<unsigned long>();
        return scene;
    }
    
    // 事件类型转换辅助方法
    static String eventTypeToString(SceneEventType type) {
        switch (type) {
            case SceneEventType::SYSTEM: return "system";
            case SceneEventType::MACRO: return "macro";
            default: return "system";
        }
    }
    
    static SceneEventType stringToEventType(const String& type) {
        if (type == "macro") return SceneEventType::MACRO;
        return SceneEventType::SYSTEM;
    }
};

/**
 * 当前活动场景状态
 */
struct ActiveScene {
    String sceneId;               // 当前活动场景ID
    String effectId;              // 当前执行的灯效ID
    unsigned long startTime;      // 开始时间
    bool isActive;                // 是否活动
    
    // 构造函数
    ActiveScene() : startTime(0), isActive(false) {}
};

/**
 * 场景管理器
 * 
 * 负责场景配置的创建、编辑、删除和持久化存储
 * 监听PrinterManager和MacrosManager的状态变化并自动切换场景
 * 提供REST API接口供前端调用
 */
class ScenesManager {
public:
    /**
     * 构造函数
     */
    ScenesManager();
    
    /**
     * 析构函数
     */
    ~ScenesManager();
    
    /**
     * 初始化场景管理器
     * 从文件系统加载已保存的场景配置
     */
    void begin();
    
    /**
     * 主循环处理函数
     * 进行必要的维护工作
     * 应在主循环中定期调用
     */
    void loop();
    
    /**
     * 注册API端点到WebServer
     * 
     * @param server AsyncWebServer实例引用
     */
    void registerAPI(AsyncWebServer& server);
    
    /**
     * 获取所有场景配置
     * 
     * @return 场景配置列表的引用
     */
    const std::vector<SceneConfig>& getScenes() const;
    
    /**
     * 根据ID获取场景配置
     * 
     * @param id 场景ID
     * @return 场景配置指针，如果不存在返回nullptr
     */
    const SceneConfig* getSceneById(const String& id) const;
    
    /**
     * 添加新场景配置
     * 
     * @param scene 要添加的场景配置
     * @return 成功返回true，失败返回false
     */
    bool addScene(const SceneConfig& scene);
    
    /**
     * 更新场景配置
     * 
     * @param id 要更新的场景ID
     * @param scene 新的场景配置数据
     * @return 成功返回true，失败返回false
     */
    bool updateScene(const String& id, const SceneConfig& scene);
    
    /**
     * 删除场景配置
     * 
     * @param id 要删除的场景ID
     * @return 成功返回true，失败返回false
     */
    bool deleteScene(const String& id);
    
    /**
     * 获取当前活动场景
     *
     * @return 当前活动场景
     */
    const ActiveScene& getActiveScene() const;

    /**
     * 检查是否有活动场景
     *
     * @return 是否有活动场景
     */
    bool hasActiveScene() const;

    /**
     * 根据场景ID获取场景名称
     *
     * @param sceneId 场景ID
     * @return 场景名称，如果不存在返回空字符串
     */
    String getSceneName(const String& sceneId) const;
    
    /**
     * 生成唯一的场景ID
     */
    String generateSceneId() const;
    
    /**
     * 验证场景配置是否有效
     * 
     * @param scene 要验证的场景配置
     * @param excludeId 验证时排除的场景ID（用于更新时）
     * @return 有效返回true，无效返回false
     */
    bool validateScene(const SceneConfig& scene, const String& excludeId = "") const;
    
    /**
     * 打印机状态变化回调函数
     * 当PrinterManager检测到状态变化时被调用
     */
    void onPrinterStatusChanged(const struct PrinterStatus& newStatus, const struct PrinterStatus& oldStatus);
    
    /**
     * 宏状态变化回调函数
     * 当MacrosManager检测到宏状态变化时被调用
     */
    void onMacroStatusChanged(const String& macroName, int newStatus, int oldStatus);

    /**
     * 设置EffectsManager引用
     * 用于执行场景关联的灯效
     *
     * @param effectsManager EffectsManager实例指针
     */
    void setEffectsManager(EffectsManager* effectsManager);

    /**
     * 进行初始场景评估
     * 在系统初始化完成后调用，检查是否有场景应该立即激活
     */
    void performInitialEvaluation();

    /**
     * 启用场景功能
     * 恢复场景自动化，重新开始监控打印机状态并自动切换灯效
     */
    void enableScenes();

    /**
     * 禁用场景功能
     * 暂停场景自动化，不再根据打印机状态自动切换灯效
     */
    void disableScenes();

    /**
     * 检查场景功能是否启用
     *
     * @return 启用返回true，禁用返回false
     */
    bool isScenesEnabled() const;

    /**
     * 强制重新激活当前场景
     * 用于预览模式退出后重新点亮LED
     */
    void forceReactivateCurrentScene();
    
    /**
     * 设置PrinterManager引用
     * 用于获取打印机状态信息
     *
     * @param printerManager PrinterManager实例指针
     */
    void setPrinterManager(PrinterManager* printerManager);
    
    /**
     * 设置MacrosManager引用
     * 用于获取宏状态信息
     *
     * @param macrosManager MacrosManager实例指针
     */
    void setMacrosManager(MacrosManager* macrosManager);

    /**
     * 评估场景变化并切换
     */
    void evaluateSceneChange();

private:
    PrinterManager* printerManager;         // PrinterManager指针
    MacrosManager* macrosManager;           // MacrosManager指针
    EffectsManager* effectsManager;         // EffectsManager指针
    std::vector<SceneConfig> scenes;        // 场景配置列表
    ActiveScene activeScene;                // 当前活动场景
    const String configFile = "/scenes.json"; // 配置文件路径
    bool scenesEnabled;                     // 场景功能启用标志
    
    /**
     * 保存场景配置到文件
     */
    bool saveToFile();
    
    /**
     * 从文件加载场景配置
     */
    bool loadFromFile();

    /**
     * 带打印机状态的场景评估
     *
     * @param currentStatus 当前打印机状态
     */
    void evaluateSceneChangeWithStatus(const struct PrinterStatus& currentStatus);
    
    /**
     * 确定应该激活的场景
     * 
     * @return 应该激活的场景ID，如果没有匹配返回空字符串
     */
    String determineActiveScene();
    
    /**
     * 切换到指定场景
     * 
     * @param sceneId 要切换到的场景ID
     */
    void switchToScene(const String& sceneId);
    
    /**
     * 将打印机状态映射为事件名称
     * 
     * @param status 打印机状态
     * @return 事件名称
     */
    String mapPrinterStatusToEvent(const struct PrinterStatus& status);
    
    /**
     * 获取可用的系统事件列表
     */
    std::vector<String> getAvailableSystemEvents() const;
    
    /**
     * 获取可用的宏事件列表
     */
    std::vector<String> getAvailableMacroEvents() const;
    
    /**
     * API处理函数
     */
    void handleGetScenes(AsyncWebServerRequest* request);
    void handleCreateScene(AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total);
    void handleUpdateScene(AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total);
    void handleDeleteScene(AsyncWebServerRequest* request);
    void handleGetAvailableEvents(AsyncWebServerRequest* request);
    
    /**
     * 工具函数
     */
    void sendJsonResponse(AsyncWebServerRequest* request, int code, const String& message, const JsonDocument* data = nullptr);
    void sendErrorResponse(AsyncWebServerRequest* request, int code, const String& message);
};

#pragma once

#include <Arduino.h>
#include <ArduinoJson.h>
#include <ESPAsyncWebServer.h>
#include <LittleFS.h>
#include <vector>
#include "Config.h"

// 前向声明
class MoonrakerClient;
class PrinterManager;
class ScenesManager;

/**
 * 宏状态枚举
 */
enum class MacroStatus {
    IDLE,       // 空闲
    RUNNING     // 运行中
};

/**
 * 宏配置数据结构
 */
struct MacroConfig {
    String id;                  // 宏唯一标识符
    String displayName;         // 用户显示名称
    String variableName;        // 宏变量名称
    MacroStatus currentStatus;  // 当前状态
    unsigned long lastUpdateTime; // 最后更新时间
    bool enabled;               // 是否启用
    
    // 构造函数
    MacroConfig() : currentStatus(MacroStatus::IDLE), lastUpdateTime(0), enabled(true) {}
    MacroConfig(const String& macroId, const String& display, const String& variable)
        : id(macroId), displayName(display), variableName(variable), 
          currentStatus(MacroStatus::IDLE), lastUpdateTime(0), enabled(true) {}
    
    // 转换为JSON对象
    void toJson(JsonObject obj) const {
        obj["id"] = id;
        obj["displayName"] = displayName;
        obj["variableName"] = variableName;
        obj["currentStatus"] = statusToString(currentStatus);
        obj["lastUpdateTime"] = lastUpdateTime;
        obj["enabled"] = enabled;
    }
    
    // 从JSON对象创建宏配置
    static MacroConfig fromJson(const JsonObject& obj) {
        MacroConfig macro;
        macro.id = obj["id"].as<String>();
        macro.displayName = obj["displayName"].as<String>();
        macro.variableName = obj["variableName"].as<String>();
        macro.currentStatus = stringToStatus(obj["currentStatus"].as<String>());
        macro.lastUpdateTime = obj["lastUpdateTime"].as<unsigned long>();
        macro.enabled = obj["enabled"].as<bool>();
        return macro;
    }
    
    // 状态转换辅助方法
    static String statusToString(MacroStatus status) {
        switch (status) {
            case MacroStatus::IDLE: return "idle";
            case MacroStatus::RUNNING: return "running";
            default: return "idle";
        }
    }
    
    static MacroStatus stringToStatus(const String& status) {
        if (status == "running") return MacroStatus::RUNNING;
        return MacroStatus::IDLE;
    }
};

/**
 * 宏管理器
 * 
 * 负责宏配置的创建、编辑、删除和持久化存储
 * 监听MoonrakerClient的宏变量变化并推送状态更新
 * 提供REST API接口供前端调用
 */
class MacrosManager {
public:
    /**
     * 构造函数
     * 
     * @param moonrakerClient MoonrakerClient实例引用
     */
    MacrosManager(MoonrakerClient& moonrakerClient);
    
    /**
     * 析构函数
     */
    ~MacrosManager();
    
    /**
     * 初始化宏管理器
     * 从文件系统加载已保存的宏配置
     */
    void begin();
    
    /**
     * 主循环处理函数
     * 检查宏状态变化并推送更新
     * 应在主循环中定期调用
     */
    void loop();
    
    /**
     * 注册API端点到WebServer
     * 
     * @param server AsyncWebServer实例引用
     */
    void registerAPI(AsyncWebServer& server);
    
    /**
     * 获取所有宏配置
     * 
     * @return 宏配置列表的引用
     */
    const std::vector<MacroConfig>& getMacros() const;
    
    /**
     * 根据ID获取宏配置
     * 
     * @param id 宏ID
     * @return 宏配置指针，如果不存在返回nullptr
     */
    const MacroConfig* getMacroById(const String& id) const;
    
    /**
     * 添加新宏配置
     * 
     * @param macro 要添加的宏配置
     * @return 成功返回true，失败返回false
     */
    bool addMacro(const MacroConfig& macro);
    
    /**
     * 更新宏配置
     * 
     * @param id 要更新的宏ID
     * @param macro 新的宏配置数据
     * @return 成功返回true，失败返回false
     */
    bool updateMacro(const String& id, const MacroConfig& macro);
    
    /**
     * 删除宏配置
     * 
     * @param id 要删除的宏ID
     * @return 成功返回true，失败返回false
     */
    bool deleteMacro(const String& id);
    
    /**
     * 获取宏统计信息
     */
    struct MacroStats {
        int totalMacros;
        int runningMacros;
        int idleMacros;
    };
    
    MacroStats getStats() const;
    
    /**
     * 生成唯一的宏ID
     */
    String generateMacroId() const;
    
    /**
     * 验证宏配置是否有效
     * 
     * @param macro 要验证的宏配置
     * @param excludeId 验证时排除的宏ID（用于更新时）
     * @return 有效返回true，无效返回false
     */
    bool validateMacro(const MacroConfig& macro, const String& excludeId = "") const;
    
    /**
     * 宏状态变化回调函数
     * 当MoonrakerClient检测到宏变量变化时被调用
     */
    void onMacroStatusChanged();

    /**
     * 设置PrinterManager引用
     * 用于通知PrinterManager推送宏状态更新
     *
     * @param printerManager PrinterManager实例指针
     */
    void setPrinterManager(PrinterManager* printerManager);

    /**
     * 设置ScenesManager引用
     * 用于通知ScenesManager宏状态变化
     *
     * @param scenesManager ScenesManager实例指针
     */
    void setScenesManager(ScenesManager* scenesManager);

private:
    MoonrakerClient& moonraker;             // MoonrakerClient引用
    PrinterManager* printerManager;         // PrinterManager指针
    ScenesManager* scenesManager;           // ScenesManager指针
    std::vector<MacroConfig> macros;        // 宏配置列表
    const String configFile = "/macros.json"; // 配置文件路径

    // 状态缓存，用于检测变化
    std::vector<MacroConfig> lastMacroStates;
    
    /**
     * 保存宏配置到文件
     */
    bool saveToFile();
    
    /**
     * 从文件加载宏配置
     */
    bool loadFromFile();
    
    /**
     * 更新宏状态
     * 从MoonrakerClient获取最新的宏变量状态
     */
    void updateMacroStates();
    
    /**
     * 检查状态是否有变化并推送更新
     */
    void checkAndBroadcastChanges();
    
    /**
     * 通知PrinterManager推送宏状态更新
     */
    void notifyPrinterManager();
    
    /**
     * API处理函数
     */
    void handleGetMacros(AsyncWebServerRequest* request);
    void handleCreateMacro(AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total);
    void handleUpdateMacro(AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total);
    void handleDeleteMacro(AsyncWebServerRequest* request);
    void handleGetStats(AsyncWebServerRequest* request);
    
    /**
     * 工具函数
     */
    void sendJsonResponse(AsyncWebServerRequest* request, int code, const String& message, const JsonDocument* data = nullptr);
    void sendErrorResponse(AsyncWebServerRequest* request, int code, const String& message);
};

#pragma once

#include <Arduino.h>
#include <WiFi.h>

/**
 * WiFi管理器类
 * 
 * 负责管理ESP32的WiFi连接，包括连接、断开检测和重连逻辑
 */
class WiFiManager {
public:
    /**
     * 构造函数
     * 
     * @param ssid WiFi网络名称
     * @param password WiFi密码
     * @param checkInterval 检查WiFi连接状态的时间间隔（毫秒）
     */
    WiFiManager(const char* ssid, const char* password, unsigned long checkInterval = 30000);
    
    /**
     * 连接到WiFi网络
     * 
     * @return 连接是否成功
     */
    bool connect();
    
    /**
     * 检查WiFi连接状态并在需要时重连
     * 应在主循环中定期调用
     * 
     * @return WiFi是否已连接
     */
    bool checkConnection();
    
    /**
     * 获取当前WiFi连接状态
     * 
     * @return WiFi是否已连接
     */
    bool isConnected() const;
    
    /**
     * 获取IP地址
     * 
     * @return 当前分配的IP地址（如果已连接）
     */
    IPAddress getIP() const;

private:
    // WiFi连接状态枚举
    enum WiFiState {
        WIFI_IDLE,          // 空闲状态
        WIFI_CONNECTING,    // 正在连接
        WIFI_CONNECTED,     // 已连接
        WIFI_RECONNECTING   // 正在重连
    };

    const char* wifiSsid;              // WiFi网络名称
    const char* wifiPassword;          // WiFi密码
    unsigned long checkInterval;       // 检查间隔（毫秒）
    unsigned long lastCheckTime;       // 上次检查时间
    unsigned long connectStartTime;    // 连接开始时间
    bool connected;                    // 连接状态
    WiFiState wifiState;               // WiFi状态机

    // 非阻塞连接的内部方法
    bool updateConnectionState();
};
#include "WebServer.h"
#include "PrinterManager.h"
#include "SegmentsManager.h"
#include "EffectsManager.h"
#include "MacrosManager.h"
#include "ScenesManager.h"

/**
 * 构造函数
 */
WebServer::WebServer(int port) : server(port), serverPort(port) {
    running = false;
    
    // 初始化模块指针
    printer = nullptr;
    segments = nullptr;
    effects = nullptr;
    scenes = nullptr;
    macros = nullptr;
}

/**
 * 析构函数
 */
WebServer::~WebServer() {
    if (running) {
        server.end();
    }
}

/**
 * 初始化Web服务器
 */
void WebServer::begin() {
    Serial.println("初始化 WebServer...");

    // 设置CORS
    setupCORS();
    
    // 设置基础路由
    setupBaseRoutes();
    
    // 设置静态文件服务
    setupStaticFiles();
    
    // 设置404处理
    server.onNotFound([this](AsyncWebServerRequest* request) {
        this->handleNotFound(request);
    });
    
    // 启动服务器
    server.begin();
    running = true;
    
    Serial.printf("WebServer 启动成功，端口: %d\n", serverPort);
}

/**
 * 注册PrinterManager模块
 */
void WebServer::registerPrinterManager(PrinterManager* printerManager) {
    if (printerManager) {
        printer = printerManager;
        printerManager->registerAPI(server);
        Serial.println("PrinterManager 模块已注册");
    }
}

/**
 * 注册其他模块（暂时为空实现，为将来扩展预留）
 */
void WebServer::registerSegmentsManager(SegmentsManager* segmentsManager) {
    if (segmentsManager) {
        segments = segmentsManager;
        segmentsManager->registerAPI(server);
        Serial.println("SegmentsManager 模块已注册");
    }
}

void WebServer::registerEffectsManager(EffectsManager* effectsManager) {
    if (effectsManager) {
        effects = effectsManager;
        effectsManager->registerAPI(server);
        Serial.println("EffectsManager 模块已注册");
    }
}

void WebServer::registerScenesManager(ScenesManager* scenesManager) {
    if (scenesManager) {
        scenes = scenesManager;
        scenesManager->registerAPI(server);
        Serial.println("ScenesManager 模块已注册");
    }
}

void WebServer::registerMacrosManager(MacrosManager* macrosManager) {
    if (macrosManager) {
        macros = macrosManager;
        macrosManager->registerAPI(server);
        Serial.println("MacrosManager 模块已注册");
    }
}

/**
 * 获取服务器状态信息
 */
String WebServer::getServerInfo() {
    StaticJsonDocument<JSON_STATUS_BUFFER_SIZE> doc;
    
    doc["server"] = "ESP32 Moonraker Monitor";
    doc["version"] = "1.0.0";
    doc["port"] = serverPort;
    doc["running"] = running;
    doc["uptime"] = millis();
    doc["free_heap"] = ESP.getFreeHeap();
    
    JsonObject modules = doc.createNestedObject("modules");
    modules["printer"] = (printer != nullptr);
    modules["segments"] = (segments != nullptr);
    modules["effects"] = (effects != nullptr);
    modules["scenes"] = (scenes != nullptr);
    modules["macros"] = (macros != nullptr);
    
    String result;
    serializeJson(doc, result);
    return result;
}

/**
 * 检查服务器是否正在运行
 */
bool WebServer::isRunning() const {
    return running;
}

/**
 * 设置静态文件服务
 */
void WebServer::setupStaticFiles() {
    // 服务静态文件，但排除API路径
    server.serveStatic("/", LittleFS, "/")
        .setDefaultFile("index.html")
        .setFilter([](AsyncWebServerRequest *request) {
            // 排除所有以 /api/ 开头的请求
            return !request->url().startsWith("/api/");
        });

    // 设置缓存头
    server.serveStatic("/css/", LittleFS, "/css/").setCacheControl("max-age=86400");
    server.serveStatic("/js/", LittleFS, "/js/").setCacheControl("max-age=86400");
    server.serveStatic("/printer/", LittleFS, "/printer/").setCacheControl("max-age=86400");

    Serial.println("静态文件服务配置完成");
}

/**
 * 设置基础API路由
 */
void WebServer::setupBaseRoutes() {
    // 根路径
    server.on("/", HTTP_GET, [this](AsyncWebServerRequest* request) {
        this->handleRoot(request);
    });
    
    // 服务器信息API
    server.on("/api/server/info", HTTP_GET, [this](AsyncWebServerRequest* request) {
        this->handleServerInfo(request);
    });
    
    // 健康检查API
    server.on("/api/health", HTTP_GET, [this](AsyncWebServerRequest* request) {
        this->handleHealthCheck(request);
    });
    
    Serial.println("基础API路由配置完成");
}

/**
 * 设置CORS头
 */
void WebServer::setupCORS() {
    DefaultHeaders::Instance().addHeader("Access-Control-Allow-Origin", "*");
    DefaultHeaders::Instance().addHeader("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS");
    DefaultHeaders::Instance().addHeader("Access-Control-Allow-Headers", "Content-Type, Authorization");
}

/**
 * 处理404错误
 */
void WebServer::handleNotFound(AsyncWebServerRequest* request) {
    String message = "File Not Found\n\n";
    message += "URI: ";
    message += request->url();
    message += "\nMethod: ";
    message += (request->method() == HTTP_GET) ? "GET" : "POST";
    message += "\nArguments: ";
    message += request->args();
    message += "\n";
    
    for (uint8_t i = 0; i < request->args(); i++) {
        message += " " + request->argName(i) + ": " + request->arg(i) + "\n";
    }
    
    request->send(404, "text/plain", message);
}

/**
 * 处理服务器信息请求
 */
void WebServer::handleServerInfo(AsyncWebServerRequest* request) {
    String info = getServerInfo();
    request->send(200, "application/json", info);
}

/**
 * 处理健康检查请求
 */
void WebServer::handleHealthCheck(AsyncWebServerRequest* request) {
    StaticJsonDocument<JSON_STATUS_BUFFER_SIZE> doc;
    doc["status"] = "ok";
    doc["timestamp"] = millis();
    doc["memory"] = ESP.getFreeHeap();
    
    String result;
    serializeJson(doc, result);
    request->send(200, "application/json", result);
}

/**
 * 处理根路径请求
 */
void WebServer::handleRoot(AsyncWebServerRequest* request) {
    // 重定向到index.html或直接服务index.html
    request->redirect("/index.html");
}

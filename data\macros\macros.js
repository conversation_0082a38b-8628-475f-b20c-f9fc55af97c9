// 宏管理模块

class MacrosManager {
    constructor() {
        this.macros = [];
        this.currentEditingMacro = null;
        this.init();
    }

    // 初始化
    async init() {
        console.log('初始化宏管理模块...');
        this.bindEvents();
        await this.loadMacros();
        this.renderMacros();

        // 初始化时也刷新宏状态显示
        this.refreshMacroStatusDisplay();
    }

    // 绑定事件
    bindEvents() {
        // 添加宏按钮
        const addBtn = document.querySelector('.add-macro-btn');
        if (addBtn) {
            addBtn.addEventListener('click', () => this.showAddModal());
        }

        // 帮助按钮
        const helpBtn = document.querySelector('.macro-help-btn');
        if (helpBtn) {
            helpBtn.addEventListener('click', () => this.showHelpModal());
        }

        // 模态框关闭事件 - 移除点击外部关闭功能

        // 关闭按钮事件
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('module-modal-macros-close') || e.target.id === 'modal-cancel' || e.target.id === 'macro-help-close') {
                this.hideModal();
            }
        });

        // 表单提交事件
        document.addEventListener('submit', (e) => {
            if (e.target.id === 'macro-form') {
                e.preventDefault();
                this.handleFormSubmit();
            }
        });

        // ESC键关闭模态框
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.hideModal();
            }
        });
    }

    // 加载宏配置
    async loadMacros() {
        try {
            const response = await fetch('/api/macros');
            if (response.ok) {
                const data = await response.json();
                if (data.success && data.data && data.data.macros) {
                    this.macros = data.data.macros;
                    console.log('加载宏配置成功:', this.macros);
                } else {
                    console.warn('宏配置数据格式异常:', data);
                    this.macros = [];
                }
            } else {
                console.error('加载宏配置失败:', response.status);
                this.macros = [];
            }
        } catch (error) {
            console.error('加载宏配置异常:', error);
            this.macros = [];
        }
    }

    // 渲染宏列表
    renderMacros() {
        const container = document.getElementById('macros-list');
        if (!container) return;

        if (this.macros.length === 0) {
            container.innerHTML = `
                <div class="macros-empty">
                    <div class="macros-empty-icon">⚙️</div>
                    <div class="macros-empty-text">暂无宏</div>
                    <div class="macros-empty-hint">点击右上角的 + 号添加新宏</div>
                </div>
            `;
        } else {
            const macroItems = this.macros.map(macro => this.createMacroItem(macro)).join('');
            container.innerHTML = macroItems;
        }
    }

    // 创建宏配置项
    createMacroItem(macro) {
        return `
            <div class="macro-item" data-macro-id="${macro.id}">
                <div class="macro-info">
                    <div class="macro-name">${macro.displayName}</div>
                    <div class="macro-variable">变量: ${macro.variableName}</div>
                </div>
                <div class="macro-actions">
                    <button class="btn-sm btn-edit" onclick="macrosManager.editMacro('${macro.id}')">编辑</button>
                    <button class="btn-sm btn-delete" onclick="macrosManager.deleteMacro('${macro.id}')">删除</button>
                </div>
            </div>
        `;
    }

    // 显示添加宏模态框
    showAddModal() {
        this.currentEditingMacro = null;
        this.showModal('添加宏事件', '', '');
    }

    // 显示帮助模态框
    showHelpModal() {
        const modal = document.getElementById('macro-help-modal');
        if (modal) {
            modal.classList.add('show');
        }
    }

    // 编辑宏
    editMacro(macroId) {
        console.log('编辑宏，ID:', macroId);
        const macro = this.macros.find(m => m.id === macroId);
        console.log('找到的宏:', macro);
        if (macro) {
            this.currentEditingMacro = macro;
            console.log('设置当前编辑宏:', this.currentEditingMacro);
            this.showModal('编辑宏事件', macro.displayName, macro.variableName);
        } else {
            console.error('未找到要编辑的宏，ID:', macroId);
        }
    }

    // 显示模态框
    showModal(title, displayName = '', variableName = '') {
        const modal = document.getElementById('macro-modal');
        const modalTitle = document.getElementById('modal-title');
        const displayNameInput = document.getElementById('macro-display-name');
        const variableNameInput = document.getElementById('macro-variable-name');

        if (!modal) return;

        // 设置标题和表单值
        if (modalTitle) modalTitle.textContent = title;
        if (displayNameInput) displayNameInput.value = displayName;
        if (variableNameInput) variableNameInput.value = variableName;

        // 清除错误信息
        this.clearFormErrors();

        // 显示模态框
        modal.classList.add('show');
    }

    // 隐藏模态框
    hideModal() {
        const modal = document.getElementById('macro-modal');
        if (modal) {
            modal.classList.remove('show');
        }

        const helpModal = document.getElementById('macro-help-modal');
        if (helpModal) {
            helpModal.classList.remove('show');
        }

        // 重置表单
        const form = document.getElementById('macro-form');
        if (form) {
            form.reset();
        }

        // 清除错误信息
        this.clearFormErrors();
    }

    // 清除表单错误信息
    clearFormErrors() {
        const errorElements = document.querySelectorAll('.macros-form-error');
        errorElements.forEach(element => {
            element.textContent = '';
        });

        const inputElements = document.querySelectorAll('.macros-form-control');
        inputElements.forEach(element => {
            element.classList.remove('error');
        });
    }

    // 处理表单提交
    async handleFormSubmit() {
        const displayName = document.getElementById('macro-display-name').value.trim();
        const variableName = document.getElementById('macro-variable-name').value.trim();

        // 清除之前的错误信息
        this.clearFormErrors();

        // 验证表单
        let hasError = false;
        if (!displayName) {
            this.showFormError('display-name-error', '请输入用户显示名称');
            hasError = true;
        }
        if (!variableName) {
            this.showFormError('variable-name-error', '请输入宏变量名称');
            hasError = true;
        }

        if (hasError) return;

        // 检查变量名是否重复（编辑时排除自己）
        const existingMacro = this.macros.find(m =>
            m.variableName === variableName &&
            (!this.currentEditingMacro || m.id !== this.currentEditingMacro.id)
        );

        if (existingMacro) {
            this.showFormError('variable-name-error', '该宏变量名称已存在');
            return;
        }

        const macroData = {
            displayName: displayName,
            variableName: variableName
        };

        try {
            let response;
            if (this.currentEditingMacro) {
                // 编辑现有宏
                response = await fetch(`/api/macros/${this.currentEditingMacro.id}`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(macroData)
                });
            } else {
                // 添加新宏
                response = await fetch('/api/macros', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(macroData)
                });
            }

            if (response.ok) {
                const result = await response.json();
                if (result.success) {
                    this.hideModal();
                    await this.loadMacros();
                    this.renderMacros();

                    // 刷新主页面的宏状态显示
                    this.refreshMacroStatusDisplay();

                    // 发送宏变化事件通知其他模块
                    document.dispatchEvent(new CustomEvent('macroChanged', {
                        detail: {
                            action: this.currentEditingMacro ? 'updated' : 'added',
                            timestamp: Date.now()
                        }
                    }));

                    this.showSuccess(this.currentEditingMacro ? '宏配置更新成功' : '宏配置创建成功');
                } else {
                    this.showError(result.message || '操作失败');
                }
            } else {
                this.showError('网络请求失败');
            }
        } catch (error) {
            console.error('提交宏配置失败:', error);
            this.showError('提交失败，请重试');
        }
    }

    // 删除宏
    async deleteMacro(macroId) {
        const macro = this.macros.find(m => m.id === macroId);
        if (!macro) return;

        if (!confirm(`确定要删除宏事件"${macro.displayName}"吗？`)) {
            return;
        }

        try {
            const response = await fetch(`/api/macros/${macroId}`, {
                method: 'DELETE'
            });

            if (response.ok) {
                const result = await response.json();
                if (result.success) {
                    await this.loadMacros();
                    this.renderMacros();

                    // 刷新主页面的宏状态显示
                    this.refreshMacroStatusDisplay();

                    // 发送宏变化事件通知其他模块
                    document.dispatchEvent(new CustomEvent('macroChanged', {
                        detail: {
                            action: 'deleted',
                            timestamp: Date.now()
                        }
                    }));

                    this.showSuccess('宏配置删除成功');
                } else {
                    this.showError(result.message || '删除失败');
                }
            } else {
                this.showError('网络请求失败');
            }
        } catch (error) {
            console.error('删除宏配置失败:', error);
            this.showError('删除失败，请重试');
        }
    }

    // 显示成功消息
    showSuccess(message) {
        // 这里可以集成到全局的通知系统
        console.log('成功:', message);
        if (window.AppUtils && window.AppUtils.showSuccess) {
            window.AppUtils.showSuccess(message);
        } else {
            alert(message);
        }
    }

    // 显示错误消息
    showError(message) {
        // 这里可以集成到全局的通知系统
        console.error('错误:', message);
        if (window.AppUtils && window.AppUtils.showError) {
            window.AppUtils.showError(message);
        } else {
            alert(message);
        }
    }

    // 显示表单错误
    showFormError(errorElementId, message) {
        const errorElement = document.getElementById(errorElementId);
        if (errorElement) {
            errorElement.textContent = message;
        }

        // 高亮对应的输入框
        const inputId = errorElementId.replace('-error', '');
        const inputElement = document.getElementById(inputId);
        if (inputElement) {
            inputElement.classList.add('error');
        }
    }

    // 刷新宏列表
    async refresh() {
        await this.loadMacros();
        this.renderMacros();
    }

    // 刷新主页面的宏状态显示
    refreshMacroStatusDisplay() {
        // 检查是否有PrinterManager实例
        if (window.PrinterManager && typeof window.PrinterManager.updateMacroStatus === 'function') {
            // 使用当前的宏配置更新状态显示
            const macroStatusData = this.macros.map(macro => ({
                id: macro.id,
                displayName: macro.displayName,
                variableName: macro.variableName,
                currentStatus: 'idle' // 默认状态，实际状态会通过WebSocket更新
            }));

            console.log('刷新宏状态显示:', macroStatusData);
            window.PrinterManager.updateMacroStatus(macroStatusData);
        } else {
            console.warn('PrinterManager未找到，无法刷新宏状态显示');
        }
    }
}

// 全局实例
let macrosManager;

// 初始化
document.addEventListener('DOMContentLoaded', function() {
    // 检查是否有宏模块
    if (document.getElementById('macros-module')) {
        macrosManager = new MacrosManager();

        // 导出到全局作用域
        window.macrosManager = macrosManager;
        console.log('宏管理模块已初始化');
    }
});

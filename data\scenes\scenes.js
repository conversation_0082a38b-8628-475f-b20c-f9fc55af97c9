// 场景预设模块

class ScenesManager {
    constructor() {
        this.scenes = [];
        this.availableEvents = {
            systemEvents: [],
            macroEvents: []
        };
        this.availableEffects = [];
        this.currentEditingId = null;
        this.init();
    }



    // 初始化
    async init() {
        this.bindEvents();
        this.bindCustomEvents();
        await this.loadScenes();
        await this.loadAvailableEvents();
        await this.loadAvailableEffects();
        this.renderScenes();
    }

    // 绑定自定义事件
    bindCustomEvents() {
        // 监听宏变化事件
        document.addEventListener('macroChanged', () => {
            this.refreshMacroEvents();
        });

        // 监听灯效变化事件
        document.addEventListener('effectChanged', () => {
            this.refreshEffects();
        });
    }

    // 绑定事件
    bindEvents() {
        // 添加场景按钮
        const addBtn = document.querySelector('#scenes-module .add-scene-btn');
        if (addBtn) {
            addBtn.addEventListener('click', () => this.showAddModal());
        }

        // 模态框关闭事件 - 只通过关闭按钮
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('module-modal-scenes-close')) {
                this.hideModal();
            }
        });

        // 取消按钮事件
        const cancelBtn = document.getElementById('scene-cancel-btn');
        if (cancelBtn) {
            cancelBtn.addEventListener('click', () => this.hideModal());
        }

        // 表单提交事件
        const form = document.getElementById('scene-form');
        if (form) {
            form.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleFormSubmit();
            });
        }
    }

    // 加载场景数据
    async loadScenes() {
        try {
            const response = await fetch('/api/scenes');
            const data = await response.json();

            if (data.success) {
                this.scenes = data.data.scenes || [];
            } else {
                console.error('加载场景失败:', data.message);
                this.showError('加载场景失败: ' + data.message);
            }
        } catch (error) {
            console.error('加载场景出错:', error);
            this.showError('加载场景出错: ' + error.message);
        }
    }

    // 加载可用事件列表
    async loadAvailableEvents() {
        try {
            const response = await fetch('/api/scenes/events');
            const data = await response.json();

            if (data.success) {
                this.availableEvents = data.data;
            } else {
                console.error('加载可用事件失败:', data.message);
            }
        } catch (error) {
            console.error('加载可用事件出错:', error);
        }
    }

    // 加载可用灯效列表
    async loadAvailableEffects() {
        try {
            const response = await fetch('/api/effects');
            const data = await response.json();

            if (data.success) {
                // 修复：API返回的是presets，不是effects
                this.availableEffects = data.data.presets || [];
            } else {
                console.error('加载可用灯效失败:', data.message);
            }
        } catch (error) {
            console.error('加载可用灯效出错:', error);
        }
    }

    // 渲染场景列表
    renderScenes() {
        const container = document.querySelector('#scenes-module .card-content');
        if (!container) return;

        if (this.scenes.length === 0) {
            container.innerHTML = `
                <div class="empty-state">
                    <div class="empty-state-icon">🎭</div>
                    <div class="empty-state-text">暂无场景</div>
                    <div class="empty-state-hint">点击右上角的 + 号添加新场景</div>
                </div>
            `;
        } else {
            let html = '<div class="scenes-list">';

            this.scenes.forEach(scene => {
                const eventIcon = this.getEventIcon(scene.eventType, scene.eventName);
                const eventDisplayName = this.getEventDisplayName(scene.eventType, scene.eventName);
                const effectDisplayName = this.getEffectDisplayName(scene.effectId);

                html += `
                    <div class="scene-item" data-id="${scene.id}">
                        <div class="scene-header">
                            <div class="scene-info">
                                <span class="scene-icon">${eventIcon}</span>
                                <span class="scene-name">${scene.name}</span>
                            </div>
                            <div class="scene-actions">
                                <button class="scene-edit-btn" onclick="scenesManager.editScene('${scene.id}')" title="编辑">编辑</button>
                                <button class="scene-delete-btn" onclick="scenesManager.deleteScene('${scene.id}')" title="删除">删除</button>
                            </div>
                        </div>
                        <div class="scene-details">
                            <div class="scene-trigger">触发: ${eventDisplayName}</div>
                            <div class="scene-effect">灯效: ${effectDisplayName}</div>
                        </div>
                    </div>
                `;
            });

            html += '</div>';
            container.innerHTML = html;
        }
    }

    // 获取事件图标
    getEventIcon(eventType, eventName) {
        if (eventType === 'system') {
            switch (eventName) {
                case 'espstart': return '🚀';
                case 'standby': return '🟢';
                case 'heating': return '🟡';
                case 'printing': return '🔵';
                case 'paused': return '⏸️';
                case 'completed': return '✅';
                case 'error': return '🔴';
                default: return '⚪';
            }
        } else {
            return '🔄';
        }
    }

    // 获取事件显示名称
    getEventDisplayName(eventType, eventName) {
        if (eventType === 'system') {
            switch (eventName) {
                case 'espstart': return 'ESP32启动';
                case 'standby': return '待机状态';
                case 'heating': return '加热中';
                case 'printing': return '打印中';
                case 'paused': return '打印暂停';
                case 'completed': return '打印完成';
                case 'error': return '错误状态';
                default: return eventName;
            }
        } else {
            // 对于宏事件，查找对应的显示名称
            if (this.availableEvents.macroEvents) {
                const macro = this.availableEvents.macroEvents.find(m =>
                    (typeof m === 'object' && m.variableName === eventName) || m === eventName
                );
                if (macro && typeof macro === 'object') {
                    return macro.displayName;
                }
            }
            return eventName;
        }
    }

    // 获取灯效显示名称
    getEffectDisplayName(effectId) {
        const effect = this.availableEffects.find(e => e.id === effectId);
        return effect ? effect.name : effectId;
    }

    // 显示添加模态框
    showAddModal() {
        this.currentEditingId = null;
        this.resetForm();
        this.updateModalTitle('添加场景预设');

        // 直接使用缓存数据，不重新加载
        this.updateEventOptions();
        this.updateEffectOptions();
        this.showModal();
    }

    // 显示编辑模态框
    showEditModal(scene) {
        this.currentEditingId = scene.id;
        this.fillForm(scene);
        this.updateModalTitle('编辑场景预设');

        // 直接使用缓存数据，不重新加载
        this.updateEventOptions();
        this.updateEffectOptions();
        this.showModal();
    }

    // 显示模态框
    showModal() {
        const modal = document.getElementById('scene-modal');
        if (modal) {
            modal.classList.add('show');
        }
    }

    // 隐藏模态框
    hideModal() {
        const modal = document.getElementById('scene-modal');
        if (modal) {
            modal.classList.remove('show');
        }
        this.resetForm();
        this.currentEditingId = null;
    }
    // 更新模态框标题
    updateModalTitle(title) {
        const titleElement = document.getElementById('scene-modal-title');
        if (titleElement) {
            titleElement.textContent = title;
        }
    }

    // 重置表单
    resetForm() {
        const form = document.getElementById('scene-form');
        if (form) {
            form.reset();
        }
        this.clearErrors();
    }

    // 填充表单
    fillForm(scene) {
        document.getElementById('scene-name').value = scene.name || '';
        document.getElementById('scene-event-name').value = scene.eventName || '';
        document.getElementById('scene-effect-id').value = scene.effectId || '';
    }

    // 更新事件选项
    updateEventOptions() {
        const eventSelect = document.getElementById('scene-event-name');
        if (!eventSelect) return;

        eventSelect.innerHTML = '';

        // 添加系统事件分组
        const systemGroup = document.createElement('optgroup');
        systemGroup.label = '系统事件';

        const systemEvents = [
            { value: 'espstart', text: 'ESP32启动' },
            { value: 'standby', text: '待机状态' },
            { value: 'heating', text: '加热中' },
            { value: 'printing', text: '打印中' },
            { value: 'paused', text: '打印暂停' },
            { value: 'completed', text: '打印完成' },
            { value: 'error', text: '错误状态' }
        ];

        systemEvents.forEach(event => {
            const option = document.createElement('option');
            option.value = event.value;
            option.textContent = event.text;
            systemGroup.appendChild(option);
        });

        eventSelect.appendChild(systemGroup);

        // 添加宏事件分组
        if (this.availableEvents.macroEvents && this.availableEvents.macroEvents.length > 0) {
            const macroGroup = document.createElement('optgroup');
            macroGroup.label = '宏事件';

            this.availableEvents.macroEvents.forEach(macro => {
                const option = document.createElement('option');
                // 如果是对象格式，使用variableName作为value，displayName作为显示文本
                if (typeof macro === 'object' && macro.variableName) {
                    option.value = macro.variableName;
                    option.textContent = macro.displayName;
                } else {
                    // 兼容旧格式
                    option.value = macro;
                    option.textContent = macro;
                }
                macroGroup.appendChild(option);
            });

            eventSelect.appendChild(macroGroup);
        }
    }

    // 更新灯效选项
    updateEffectOptions() {
        const effectSelect = document.getElementById('scene-effect-id');
        if (!effectSelect) return;

        effectSelect.innerHTML = '';

        if (this.availableEffects.length === 0) {
            const option = document.createElement('option');
            option.value = '';
            option.textContent = '暂无可用灯效';
            option.disabled = true;
            effectSelect.appendChild(option);
        } else {
            this.availableEffects.forEach(effect => {
                const option = document.createElement('option');
                option.value = effect.id;
                option.textContent = effect.name;  // 只显示灯效名称，不显示ID
                effectSelect.appendChild(option);
            });
        }
    }

    // 处理表单提交
    async handleFormSubmit() {
        const formData = this.getFormData();

        if (!this.validateForm(formData)) {
            return;
        }

        try {
            let response;
            if (this.currentEditingId) {
                // 更新场景
                response = await fetch(`/api/scenes/${this.currentEditingId}`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(formData)
                });
            } else {
                // 创建场景
                response = await fetch('/api/scenes', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(formData)
                });
            }

            const data = await response.json();

            if (data.success) {
                this.showSuccess(data.message);
                this.hideModal();
                await this.loadScenes();
                this.renderScenes();
            } else {
                this.showError(data.message);
            }
        } catch (error) {
            console.error('提交场景失败:', error);
            this.showError('提交场景失败: ' + error.message);
        }
    }

    // 获取表单数据
    getFormData() {
        const eventName = document.getElementById('scene-event-name').value;

        // 判断事件类型
        const systemEvents = ['espstart', 'standby', 'heating', 'printing', 'paused', 'completed', 'error'];
        const eventType = systemEvents.includes(eventName) ? 'system' : 'macro';

        return {
            name: document.getElementById('scene-name').value.trim(),
            eventType: eventType,
            eventName: eventName,
            effectId: document.getElementById('scene-effect-id').value.trim()
        };
    }

    // 验证表单
    validateForm(formData) {
        this.clearErrors();
        let isValid = true;

        if (!formData.name) {
            this.showFieldError('scene-name-error', '请输入场景名称');
            isValid = false;
        }

        if (!formData.eventName) {
            this.showFieldError('scene-event-error', '请选择触发事件');
            isValid = false;
        }

        if (!formData.effectId) {
            this.showFieldError('scene-effect-error', '请选择关联灯效');
            isValid = false;
        }

        return isValid;
    }

    // 编辑场景
    editScene(sceneId) {
        const scene = this.scenes.find(s => s.id === sceneId);
        if (scene) {
            this.showEditModal(scene);
        }
    }

    // 删除场景
    async deleteScene(sceneId) {
        const scene = this.scenes.find(s => s.id === sceneId);
        if (!scene) return;

        if (!confirm(`确定要删除场景 "${scene.name}" 吗？`)) {
            return;
        }

        try {
            const response = await fetch(`/api/scenes/${sceneId}`, {
                method: 'DELETE'
            });

            const data = await response.json();

            if (data.success) {
                this.showSuccess(data.message);
                await this.loadScenes();
                this.renderScenes();
            } else {
                this.showError(data.message);
            }
        } catch (error) {
            console.error('删除场景失败:', error);
            this.showError('删除场景失败: ' + error.message);
        }
    }

    // 工具方法
    showError(message) {
        if (window.AppUtils && window.AppUtils.showError) {
            window.AppUtils.showError(message);
        } else {
            alert('错误: ' + message);
        }
    }

    showSuccess(message) {
        if (window.AppUtils && window.AppUtils.showSuccess) {
            window.AppUtils.showSuccess(message);
        } else {
            alert('成功: ' + message);
        }
    }

    showFieldError(fieldId, message) {
        const errorElement = document.getElementById(fieldId);
        if (errorElement) {
            errorElement.textContent = message;
            errorElement.classList.add('show');
        }
    }

    clearErrors() {
        const errorElements = document.querySelectorAll('.scenes-form-error');
        errorElements.forEach(element => {
            element.textContent = '';
            element.classList.remove('show');
        });
    }

    // 刷新宏事件列表
    async refreshMacroEvents() {
        await this.loadAvailableEvents();

        // 如果模态框正在显示，立即更新下拉选项
        const modal = document.getElementById('scene-modal');
        if (modal && modal.classList.contains('show')) {
            this.updateEventOptions();
        }
    }

    // 刷新灯效列表
    async refreshEffects() {
        await this.loadAvailableEffects();

        // 如果模态框正在显示，立即更新灯效选项
        const modal = document.getElementById('scene-modal');
        if (modal && modal.classList.contains('show')) {
            this.updateEffectOptions();
        }
    }




}

// 全局实例
let scenesManager;

// 初始化
document.addEventListener('DOMContentLoaded', function() {
    scenesManager = new ScenesManager();
    console.log('场景预设模块已初始化');
});

#include "MoonrakerClient.h"
#include "Config.h"

// 初始化静态实例指针
MoonrakerClient* MoonrakerClient::instance = nullptr;

// 初始化静态JSON文档
StaticJsonDocument<JSON_MESSAGE_BUFFER_SIZE> MoonrakerClient::messageDoc;
StaticJsonDocument<JSON_SEND_BUFFER_SIZE> MoonrakerClient::sendDoc;

/**
 * 构造函数 - 初始化所有状态变量
 */
MoonrakerClient::MoonrakerClient() {
    printerState = "standby";
    printProgress = 0.0;
    filename = "";
    extruderTemp = 0.0;
    extruderTarget = 0.0;
    bedTemp = 0.0;
    bedTarget = 0.0;
    lastReconnectAttempt = 0;

    // 初始化状态变化检测变量
    lastPrinterState = "";
    lastPrintProgress = -1.0;
    lastFilename = "";
    lastExtruderTemp = -1.0;
    lastExtruderTarget = -1.0;
    lastBedTemp = -1.0;
    lastBedTarget = -1.0;

    // 初始化错误计数器
    reconnectAttempts = 0;
    jsonParseErrors = 0;
    lastErrorReset = millis();

    // 安全地设置全局实例指针
    if (instance == nullptr) {
        instance = this;
    } else {
        Serial.println("警告: 尝试创建多个MoonrakerClient实例!");
    }
}

/**
 * 析构函数 - 清理资源
 */
MoonrakerClient::~MoonrakerClient() {
    // 清理实例指针
    if (instance == this) {
        instance = nullptr;
    }
}

/**
 * 初始化客户端
 * 
 * @param host Moonraker服务器地址
 * @param port Moonraker服务器端口
 */
void MoonrakerClient::begin(const char* host, int port) {
    moonrakerHost = String(host);
    moonrakerPort = port;
    
    // 设置WebSocket回调
    client.onMessage(staticOnMessage);
    client.onEvent(staticOnEvent);
    client.addHeader("Origin", "http://esp32.local");
}

/**
 * 连接到Moonraker WebSocket服务器
 */
void MoonrakerClient::connect() {
    Serial.println("尝试连接到 Moonraker WebSocket...");
    bool connected = client.connect(moonrakerHost.c_str(), moonrakerPort, "/websocket");
    
    if (connected) {
        Serial.println("成功连接到 Moonraker WebSocket!");
        lastReconnectAttempt = millis();
    } else {
        Serial.println("连接到 Moonraker WebSocket 失败!");
    }
}

/**
 * 处理WebSocket消息并检查连接状态
 * 应在主循环中定期调用
 */
void MoonrakerClient::poll() {
    if (client.available()) {
        client.poll();
        // 连接正常，重置重连计数器
        if (reconnectAttempts > 0) {
            reconnectAttempts = 0;
            Serial.println("WebSocket连接已恢复");
        }
    } else {
        // 检查是否需要重连
        unsigned long currentTime = millis();
        if (currentTime - lastReconnectAttempt > RECONNECT_INTERVAL) {
            if (reconnectAttempts < MAX_RECONNECT_ATTEMPTS) {
                Serial.printf("WebSocket连接已断开，尝试重新连接... (%d/%d)\n",
                             reconnectAttempts + 1, MAX_RECONNECT_ATTEMPTS);
                connect();
                reconnectAttempts++;
                lastReconnectAttempt = currentTime;
            } else {
                handleError("MAX_RECONNECT");
            }
        }
    }

    // 定期重置错误计数器
    unsigned long currentTime = millis();
    if (currentTime - lastErrorReset > ERROR_RESET_INTERVAL) {
        resetErrorCounters();
    }
}

/**
 * 检查WebSocket连接状态
 * 
 * @return 连接状态 true=已连接，false=未连接
 */
bool MoonrakerClient::isConnected() {
    return client.available(false);
}

// ===== 状态获取方法 =====

String MoonrakerClient::getPrinterState() const {
    return printerState;
}

float MoonrakerClient::getPrintProgress() const {
    return printProgress;
}

String MoonrakerClient::getFilename() const {
    return filename;
}

float MoonrakerClient::getExtruderTemp() const {
    return extruderTemp;
}

float MoonrakerClient::getExtruderTarget() const {
    return extruderTarget;
}

float MoonrakerClient::getBedTemp() const {
    return bedTemp;
}

float MoonrakerClient::getBedTarget() const {
    return bedTarget;
}

String MoonrakerClient::getMacroVariable(const String& variableName) const {
    if (macroVariables.containsKey(variableName)) {
        return macroVariables[variableName].as<String>();
    }
    return "";
}

/**
 * WebSocket消息处理回调
 * 解析从Moonraker服务器接收的JSON消息
 */
void MoonrakerClient::onMessage(websockets::WebsocketsMessage message) {
    // 清空静态文档并重用
    messageDoc.clear();

    // 检查消息大小是否超过静态缓冲区
    if (message.data().length() > messageDoc.capacity()) {
#if ENABLE_SERIAL_STATUS_OUTPUT
        Serial.printf("消息过大 (%d bytes)，跳过处理\n", message.data().length());
#endif
        return;
    }

    DeserializationError error = deserializeJson(messageDoc, message.data());

    if (error) {
        Serial.printf("JSON解析失败 (错误码: %d): %s\n", ERROR_JSON_PARSE, error.c_str());
        handleError("JSON_PARSE");
        return;
    }

    // 处理server.info响应
    if (messageDoc.containsKey("result") && messageDoc["result"].containsKey("klippy_state")) {
        String state = messageDoc["result"]["klippy_state"].as<String>();
        Serial.println("Klipper状态: " + state);
        return;
    }

    // 处理订阅响应
    if (messageDoc.containsKey("id") && messageDoc["id"] == 1 && messageDoc.containsKey("result")) {
        Serial.println("收到订阅响应");
        return;
    }

    // 处理通知消息
    if (messageDoc.containsKey("method")) {
        String method = messageDoc["method"].as<String>();
        
        if (method == "notify_klippy_disconnected") {
            Serial.println("通知: Klipper断开连接");
            resetAllStates();
            return;
        }
        
        if (method == "notify_klippy_shutdown") {
            Serial.println("通知: Klipper关闭");
            resetAllStates();
            return;
        }
        
        if (method == "notify_klippy_ready") {
            Serial.println("通知: Klipper已就绪");
            resetAllStates();
            subscribeToObjects();
            return;
        }
        
        if (method == "notify_proc_stat_update") {
            // 这些消息在FIRMWARE_RESTART后可能仍然包含旧数据
            // 不处理这些消息的状态更新
            return;
        }
    }

    // 提取状态对象
    JsonObject status_obj;
    bool found_status = false;

    if (messageDoc.containsKey("result") && messageDoc["result"].containsKey("status")) {
        status_obj = messageDoc["result"]["status"];
        found_status = true;
    } else if (messageDoc.containsKey("params") && messageDoc["params"].size() > 0 &&
              (!messageDoc.containsKey("method") || messageDoc["method"] != "notify_proc_stat_update")) {
        status_obj = messageDoc["params"][0];
        found_status = true;
    }
    
    if (!found_status) {
        return;
    }

    // 解析打印状态 (print_stats)
    if (status_obj.containsKey("print_stats")) {
        JsonObject stats = status_obj["print_stats"];
        
        if (stats.containsKey("state")) {
            String newState = stats["state"].as<String>();
            if (newState != printerState) {
                Serial.printf("打印机状态变化: %s -> %s\n", printerState.c_str(), newState.c_str());
            }
            printerState = newState;
        }

        if (stats.containsKey("filename")) {
            filename = stats["filename"].as<String>();
        }

        if (stats.containsKey("progress")) {
            printProgress = stats["progress"].as<float>();
        }
    }

    // 解析温度数据
    if (status_obj.containsKey("extruder")) {
        JsonObject extruder = status_obj["extruder"];
        if (extruder.containsKey("temperature")) {
            extruderTemp = extruder["temperature"].as<float>();
        }
        if (extruder.containsKey("target")) {
            extruderTarget = extruder["target"].as<float>();
        }
    }

    if (status_obj.containsKey("heater_bed")) {
        JsonObject bed = status_obj["heater_bed"];
        if (bed.containsKey("temperature")) {
            bedTemp = bed["temperature"].as<float>();
        }
        if (bed.containsKey("target")) {
            bedTarget = bed["target"].as<float>();
        }
    }

    // 解析宏变量数据
    String macroObjectName = "gcode_macro " MACRO_STATUS_NAME;
    if (status_obj.containsKey(macroObjectName)) {
        JsonObject macroObj = status_obj[macroObjectName];
        // 清空之前的宏变量数据
        macroVariables.clear();
        // 复制新的宏变量数据
        macroVariables.set(macroObj);

#if ENABLE_SERIAL_STATUS_OUTPUT
        Serial.println("更新宏变量数据");
#endif
    }

    // 输出状态信息到串口（仅在状态变化时）
    printStatusIfChanged();

    // 立即通知状态变化，推送给前端
    if (statusChangeCallback) {
        statusChangeCallback();
    }
}

/**
 * WebSocket事件处理回调
 * 处理连接、断开等事件
 */
void MoonrakerClient::onEvent(websockets::WebsocketsEvent event, String data) {
    if(event == websockets::WebsocketsEvent::ConnectionOpened) {
        Serial.println("[WSc] 连接已建立");
        
        // 发送客户端标识
        sendDoc.clear();
        sendDoc["jsonrpc"] = "2.0";
        sendDoc["method"] = "server.connection.identify";
        JsonObject notifyParams = sendDoc.createNestedObject("params");
        notifyParams["client_name"] = "ESP32 Monitor";
        notifyParams["version"] = "1.0.0";
        notifyParams["type"] = "web";
        notifyParams["url"] = "http://esp32.local";
        sendDoc["id"] = 9999;

        String identify_msg;
        serializeJson(sendDoc, identify_msg);
        client.send(identify_msg);

        // 查询服务器状态
        sendDoc.clear();
        sendDoc["jsonrpc"] = "2.0";
        sendDoc["method"] = "server.info";
        sendDoc["id"] = 5678;

        String info_msg;
        serializeJson(sendDoc, info_msg);
        
        Serial.println("查询服务器信息: " + info_msg);
        client.send(info_msg);
        
        // 订阅打印机对象
        subscribeToObjects();

    } else if(event == websockets::WebsocketsEvent::ConnectionClosed) {
        Serial.println("[WSc] 连接已关闭");
        // 连接关闭后会通过poll()函数自动尝试重连
    } else if(event == websockets::WebsocketsEvent::GotPing) {
        Serial.println("[WSc] 收到Ping");
    } else if(event == websockets::WebsocketsEvent::GotPong) {
        Serial.println("[WSc] 收到Pong");
    }
}

/**
 * 订阅Moonraker对象更新
 * 用于接收打印机状态、温度等实时更新
 */
void MoonrakerClient::subscribeToObjects() {
    sendDoc.clear();
    JsonObject params = sendDoc.createNestedObject("params");
    JsonObject objects = params.createNestedObject("objects");
    JsonVariant null_value; // 空的JsonVariant会被序列化为 'null'

    // 添加需要订阅的对象
    objects["print_stats"] = null_value;
    objects["heater_bed"] = null_value;
    objects["extruder"] = null_value;
    objects["gcode_macro " MACRO_STATUS_NAME] = null_value;

    sendDoc["jsonrpc"] = "2.0";
    sendDoc["method"] = "printer.objects.subscribe";
    sendDoc["id"] = 1;

    String subscribe_msg;
    serializeJson(sendDoc, subscribe_msg);
    
    Serial.println("发送订阅请求: " + subscribe_msg);
    client.send(subscribe_msg);
}

/**
 * 重置所有状态变量
 * 在Klipper重启或断开连接时调用
 */
void MoonrakerClient::resetAllStates() {
    printerState = "standby";
    printProgress = 0.0;
    filename = "";
    extruderTarget = 0.0;
    bedTarget = 0.0;
    
    Serial.println("已重置所有状态变量");

    // 输出当前状态
#if ENABLE_SERIAL_STATUS_OUTPUT
    Serial.println("===== 打印机状态 =====");
    Serial.printf("打印机状态: %s\n", printerState.c_str());
    Serial.printf("温度 - 喷嘴: %.1f/%.1f°C, 热床: %.1f/%.1f°C\n",
                 extruderTemp, extruderTarget, bedTemp, bedTarget);
    Serial.println("=====================");
#endif
}



/**
 * 输出状态信息到串口（仅在状态变化时）
 */
void MoonrakerClient::printStatusIfChanged() {
#if ENABLE_SERIAL_STATUS_OUTPUT
    // 检查状态是否发生变化
    bool hasChanged = (printerState != lastPrinterState ||
                      abs(printProgress - lastPrintProgress) > 0.001f ||
                      filename != lastFilename ||
                      abs(extruderTemp - lastExtruderTemp) > 0.1f ||
                      abs(extruderTarget - lastExtruderTarget) > 0.1f ||
                      abs(bedTemp - lastBedTemp) > 0.1f ||
                      abs(bedTarget - lastBedTarget) > 0.1f);

    if (hasChanged) {
        Serial.println("===== 打印机状态 =====");
        Serial.printf("打印机状态: %s\n", printerState.c_str());
        Serial.printf("温度 - 喷嘴: %.1f/%.1f°C, 热床: %.1f/%.1f°C\n",
                     extruderTemp, extruderTarget, bedTemp, bedTarget);
        if (printerState == "printing") {
            Serial.printf("打印文件: %s\n", filename.c_str());
            Serial.printf("打印进度: %.1f%%\n", printProgress * 100);
        }
        Serial.println("=====================");

        // 更新上次状态记录
        lastPrinterState = printerState;
        lastPrintProgress = printProgress;
        lastFilename = filename;
        lastExtruderTemp = extruderTemp;
        lastExtruderTarget = extruderTarget;
        lastBedTemp = bedTemp;
        lastBedTarget = bedTarget;
    }
#endif
}

// 静态回调函数，用于桥接到类实例
void MoonrakerClient::staticOnMessage(websockets::WebsocketsClient& client, websockets::WebsocketsMessage message) {
    if (instance) {
        instance->onMessage(message);
    }
}

void MoonrakerClient::staticOnEvent(websockets::WebsocketsClient& client, websockets::WebsocketsEvent event, String data) {
    if (instance) {
        instance->onEvent(event, data);
    }
}

/**
 * 处理错误并检查是否需要重置
 */
void MoonrakerClient::handleError(const String& errorType) {
    if (errorType == "JSON_PARSE") {
        jsonParseErrors++;
        Serial.printf("JSON解析错误计数: %d (错误码: %d)\n", jsonParseErrors, ERROR_JSON_PARSE);

        if (jsonParseErrors > MAX_JSON_PARSE_ERRORS) {
            Serial.printf("JSON解析错误过多，重置连接... (错误码: %d)\n", ERROR_JSON_PARSE);
            client.close();
            resetAllStates();
            jsonParseErrors = 0;
        }
    } else if (errorType == "MAX_RECONNECT") {
        Serial.printf("达到最大重连次数，等待更长时间后重试... (错误码: %d)\n", ERROR_WEBSOCKET_CONNECTION);
        lastReconnectAttempt = millis() + (RECONNECT_INTERVAL * 5);  // 等待5倍时间
        reconnectAttempts = 0;
    }
}

/**
 * 重置错误计数器
 */
void MoonrakerClient::resetErrorCounters() {
    if (jsonParseErrors > 0 || reconnectAttempts > 0) {
        Serial.println("重置错误计数器");
    }
    jsonParseErrors = 0;
    reconnectAttempts = 0;
    lastErrorReset = millis();
}
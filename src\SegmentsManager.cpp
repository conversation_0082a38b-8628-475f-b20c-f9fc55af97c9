#include "SegmentsManager.h"
#include "Config.h"

/**
 * 构造函数
 */
SegmentsManager::SegmentsManager(int totalLeds) : totalLeds(totalLeds) {
    Serial.printf("SegmentsManager 初始化，总LED数量: %d\n", totalLeds);
}

/**
 * 析构函数
 */
SegmentsManager::~SegmentsManager() {
}

/**
 * 初始化分段管理器
 */
void SegmentsManager::begin() {
    Serial.println("初始化 SegmentsManager...");
    
    // 从文件加载配置
    loadFromFile();
}

/**
 * 注册API端点到WebServer
 */
void SegmentsManager::registerAPI(AsyncWebServer& server) {
    Serial.println("SegmentsManager API端点已注册");

    // 获取所有分段
    server.on("/api/segments", HTTP_GET, [this](AsyncWebServerRequest* request) {
        this->handleGetSegments(request);
    });

    // 获取分段统计信息
    server.on("/api/segments/stats", HTTP_GET, [this](AsyncWebServerRequest* request) {
        this->handleGetStats(request);
    });

    // 创建新分段
    server.on("/api/segments", HTTP_POST,
        [this](AsyncWebServerRequest* request) {
            // POST请求的响应在body处理函数中处理
        },
        nullptr,
        [this](AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total) {
            this->handleCreateSegment(request, data, len, index, total);
        }
    );

    // 更新分段 - 使用通配符路由
    server.on("/api/segments/*", HTTP_PUT,
        [this](AsyncWebServerRequest* request) {
            // PUT请求的响应在body处理函数中处理
        },
        nullptr,
        [this](AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total) {
            this->handleUpdateSegment(request, data, len, index, total);
        }
    );

    // 删除分段 - 使用通配符路由
    server.on("/api/segments/*", HTTP_DELETE, [this](AsyncWebServerRequest* request) {
        this->handleDeleteSegment(request);
    });
}

/**
 * 获取所有分段
 */
const std::vector<LEDSegment>& SegmentsManager::getSegments() const {
    return segments;
}

/**
 * 根据ID获取分段
 */
const LEDSegment* SegmentsManager::getSegmentById(const String& id) const {
    for (const auto& segment : segments) {
        if (segment.id == id) {
            return &segment;
        }
    }
    return nullptr;
}

/**
 * 添加新分段
 */
bool SegmentsManager::addSegment(const LEDSegment& segment) {
    // 验证分段
    if (!validateSegment(segment)) {
        return false;
    }

    // 检查ID是否已存在
    if (getSegmentById(segment.id) != nullptr) {
        return false;
    }
    
    // 添加分段
    segments.push_back(segment);
    
    // 保存到文件
    if (saveToFile()) {
        Serial.printf("[Segments] 添加成功: %s (ID: %s) - 位置: %d-%d\n",
                     segment.name.c_str(), segment.id.c_str(), segment.startLed, segment.endLed);
        return true;
    } else {
        // 保存失败，回滚
        segments.pop_back();
        return false;
    }
}

/**
 * 更新分段
 */
bool SegmentsManager::updateSegment(const String& id, const LEDSegment& segment) {
    // 验证分段（排除当前分段）
    if (!validateSegment(segment, id)) {
        return false;
    }
    
    // 查找并更新分段
    for (auto& seg : segments) {
        if (seg.id == id) {
            LEDSegment oldSegment = seg;  // 备份原数据
            seg = segment;
            seg.id = id;  // 保持ID不变
            
            // 保存到文件
            if (saveToFile()) {
                Serial.printf("[Segments] 更新成功: %s (ID: %s) - 位置: %d-%d\n",
                             segment.name.c_str(), id.c_str(), segment.startLed, segment.endLed);
                return true;
            } else {
                // 保存失败，回滚
                seg = oldSegment;
                return false;
            }
        }
    }

    return false;
}

/**
 * 删除分段
 */
bool SegmentsManager::deleteSegment(const String& id) {
    for (auto it = segments.begin(); it != segments.end(); ++it) {
        if (it->id == id) {
            LEDSegment deletedSegment = *it;  // 备份数据
            segments.erase(it);
            
            // 保存到文件
            if (saveToFile()) {
                Serial.printf("[Segments] 删除成功: %s (ID: %s)\n",
                             deletedSegment.name.c_str(), deletedSegment.id.c_str());
                return true;
            } else {
                // 保存失败，回滚
                segments.insert(it, deletedSegment);
                return false;
            }
        }
    }

    return false;
}

/**
 * 获取总LED数量
 */
int SegmentsManager::getTotalLeds() const {
    return totalLeds;
}

/**
 * 获取已使用的LED数量
 */
int SegmentsManager::getUsedLeds() const {
    int used = 0;
    for (const auto& segment : segments) {
        used += segment.getLength();
    }
    return used;
}

/**
 * 获取剩余可用的LED数量
 */
int SegmentsManager::getAvailableLeds() const {
    return totalLeds - getUsedLeds();
}

/**
 * 获取下一个分段的建议起始位置
 */
int SegmentsManager::getNextStartPosition() const {
    if (segments.empty()) {
        return 1;  // 第一个分段从1开始
    }
    
    // 找到最大的结束位置
    int maxEnd = 0;
    for (const auto& segment : segments) {
        if (segment.endLed > maxEnd) {
            maxEnd = segment.endLed;
        }
    }
    
    return maxEnd + 1;
}

/**
 * 验证分段是否有效
 */
bool SegmentsManager::validateSegment(const LEDSegment& segment, const String& excludeId) const {
    // 检查范围是否有效
    if (segment.startLed < 1 || segment.endLed > totalLeds || segment.startLed > segment.endLed) {
        return false;
    }

    // 检查名称是否为空
    if (segment.name.isEmpty()) {
        return false;
    }
    
    // 检查是否与其他分段重叠
    for (const auto& existingSegment : segments) {
        if (existingSegment.id == excludeId) {
            continue;  // 跳过被排除的分段
        }
        
        if (segment.overlapsWith(existingSegment)) {
            return false;
        }
    }
    
    return true;
}

/**
 * 生成唯一的分段ID
 */
String SegmentsManager::generateSegmentId() const {
    return generateUniqueId("segments");
}

/**
 * 保存分段配置到文件
 */
bool SegmentsManager::saveToFile() {
    StaticJsonDocument<JSON_SEGMENTS_BUFFER_SIZE> doc;
    JsonArray segmentsArray = doc.createNestedArray("segments");

    // 使用统一的序列化方法
    for (const auto& segment : segments) {
        JsonObject segObj = segmentsArray.createNestedObject();
        segObj["id"] = segment.id;
        segObj["name"] = segment.name;
        segObj["startLed"] = segment.startLed;
        segObj["endLed"] = segment.endLed;
    }

    doc["totalLeds"] = totalLeds;
    doc["version"] = "1.0";

    File file = LittleFS.open(configFile, "w");
    if (!file) {
        return false;
    }

    size_t bytesWritten = serializeJson(doc, file);
    file.close();

    return bytesWritten > 0;
}

/**
 * 从文件加载分段配置
 */
bool SegmentsManager::loadFromFile() {
    if (!LittleFS.exists(configFile)) {
        return false;
    }

    File file = LittleFS.open(configFile, "r");
    if (!file) {
        return false;
    }

    StaticJsonDocument<2048> doc;
    DeserializationError error = deserializeJson(doc, file);
    file.close();

    if (error) {
        return false;
    }

    // 清空现有分段
    segments.clear();

    // 加载分段数据
    JsonArray segmentsArray = doc["segments"];
    for (JsonObject segObj : segmentsArray) {
        LEDSegment segment;
        segment.id = segObj["id"].as<String>();
        segment.name = segObj["name"].as<String>();
        segment.startLed = segObj["startLed"].as<int>();
        segment.endLed = segObj["endLed"].as<int>();

        // 验证分段数据
        if (validateSegment(segment)) {
            segments.push_back(segment);
        }
    }

    return true;
}

/**
 * 处理获取所有分段的请求
 */
void SegmentsManager::handleGetSegments(AsyncWebServerRequest* request) {
    StaticJsonDocument<JSON_SEGMENTS_BUFFER_SIZE> doc;
    JsonArray segmentsArray = doc.createNestedArray("segments");

    // 使用统一的序列化方法
    for (const auto& segment : segments) {
        JsonObject segObj = segmentsArray.createNestedObject();
        segObj["id"] = segment.id;
        segObj["name"] = segment.name;
        segObj["startLed"] = segment.startLed;
        segObj["endLed"] = segment.endLed;
        segObj["length"] = segment.getLength();
    }

    JsonObject stats = doc.createNestedObject("stats");
    stats["totalLeds"] = totalLeds;
    stats["usedLeds"] = getUsedLeds();
    stats["availableLeds"] = getAvailableLeds();
    stats["nextStartPosition"] = getNextStartPosition();

    sendJsonResponse(request, 200, "获取分段列表成功", &doc);
}

/**
 * 处理获取统计信息的请求
 */
void SegmentsManager::handleGetStats(AsyncWebServerRequest* request) {
    StaticJsonDocument<JSON_STATUS_BUFFER_SIZE> doc;
    JsonObject stats = doc.createNestedObject("stats");
    stats["totalLeds"] = totalLeds;
    stats["usedLeds"] = getUsedLeds();
    stats["availableLeds"] = getAvailableLeds();
    stats["nextStartPosition"] = getNextStartPosition();
    stats["segmentCount"] = segments.size();

    sendJsonResponse(request, 200, "获取统计信息成功", &doc);
}

/**
 * 处理创建分段的请求
 */
void SegmentsManager::handleCreateSegment(AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total) {
    if (index + len == total) {  // 确保接收完整数据
        String body = String((char*)data, len);

        StaticJsonDocument<512> doc;
        DeserializationError error = deserializeJson(doc, body);

        if (error) {
            sendErrorResponse(request, 400, "JSON格式无效");
            return;
        }

        // 创建分段对象
        LEDSegment segment;
        segment.id = generateSegmentId();
        segment.name = doc["name"].as<String>();
        segment.startLed = doc["startLed"].as<int>();
        segment.endLed = doc["endLed"].as<int>();

        // 添加分段
        if (addSegment(segment)) {
            StaticJsonDocument<256> responseDoc;
            JsonObject segObj = responseDoc.createNestedObject("segment");
            segObj["id"] = segment.id;
            segObj["name"] = segment.name;
            segObj["startLed"] = segment.startLed;
            segObj["endLed"] = segment.endLed;
            segObj["length"] = segment.getLength();

            sendJsonResponse(request, HTTP_OK, "分段创建成功", &responseDoc);
        } else {
            sendErrorResponse(request, 400, "分段创建失败");
        }
    }
}

/**
 * 处理更新分段的请求
 */
void SegmentsManager::handleUpdateSegment(AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total) {
    if (index + len == total) {  // 确保接收完整数据
        // 从URL中提取分段ID
        String url = request->url();
        String segmentId = url.substring(url.lastIndexOf('/') + 1);
        String body = String((char*)data, len);

        StaticJsonDocument<512> doc;
        DeserializationError error = deserializeJson(doc, body);

        if (error) {
            sendErrorResponse(request, 400, "JSON格式无效");
            return;
        }

        // 创建分段对象
        LEDSegment segment;
        segment.name = doc["name"].as<String>();
        segment.startLed = doc["startLed"].as<int>();
        segment.endLed = doc["endLed"].as<int>();

        // 更新分段
        if (updateSegment(segmentId, segment)) {
            StaticJsonDocument<256> responseDoc;
            JsonObject segObj = responseDoc.createNestedObject("segment");
            segObj["id"] = segmentId;
            segObj["name"] = segment.name;
            segObj["startLed"] = segment.startLed;
            segObj["endLed"] = segment.endLed;
            segObj["length"] = segment.getLength();

            sendJsonResponse(request, 200, "分段更新成功", &responseDoc);
        } else {
            sendErrorResponse(request, 400, "分段更新失败");
        }
    }
}

/**
 * 处理删除分段的请求
 */
void SegmentsManager::handleDeleteSegment(AsyncWebServerRequest* request) {
    // 从URL中提取分段ID
    String url = request->url();
    String segmentId = url.substring(url.lastIndexOf('/') + 1);

    if (deleteSegment(segmentId)) {
        StaticJsonDocument<128> responseDoc;
        responseDoc["deletedId"] = segmentId;
        sendJsonResponse(request, 200, "分段删除成功", &responseDoc);
    } else {
        sendErrorResponse(request, 404, "分段不存在");
    }
}

/**
 * 发送JSON响应
 */
void SegmentsManager::sendJsonResponse(AsyncWebServerRequest* request, int code, const String& message, const JsonDocument* data) {
    StaticJsonDocument<JSON_RESPONSE_BUFFER_SIZE> responseDoc;
    responseDoc["success"] = (code == HTTP_OK);
    responseDoc["message"] = message;
    responseDoc["timestamp"] = millis();

    if (data != nullptr && !data->isNull()) {
        responseDoc["data"] = *data;
    }

    String response;
    serializeJson(responseDoc, response);

    request->send(code, "application/json", response);
}

/**
 * 发送错误响应
 */
void SegmentsManager::sendErrorResponse(AsyncWebServerRequest* request, int code, const String& message) {
    sendJsonResponse(request, code, message, nullptr);
}

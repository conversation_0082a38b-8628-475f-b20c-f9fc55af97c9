#pragma once

#include <Arduino.h>
#include <ArduinoWebsockets.h>
#include <ArduinoJson.h>
#include "Config.h"

/**
 * Moonraker客户端类
 * 
 * 负责与Moonraker服务器建立WebSocket连接并处理通信
 * 实现了状态监控、温度读取等功能
 */
class MoonrakerClient {
public:
    /**
     * 构造函数 - 初始化所有状态变量
     */
    MoonrakerClient();

    /**
     * 析构函数 - 清理资源
     */
    ~MoonrakerClient();
    
    /**
     * 初始化客户端
     * 
     * @param host Moonraker服务器地址
     * @param port Moonraker服务器端口
     */
    void begin(const char* host, int port);

    /**
     * 连接到Moonraker WebSocket服务器
     */
    void connect();

    /**
     * 处理WebSocket消息并检查连接状态
     * 应在主循环中定期调用
     */
    void poll();

    /**
     * 检查WebSocket连接状态
     * 
     * @return 连接状态 true=已连接，false=未连接
     */
    bool isConnected();
    
    // ===== 状态获取方法 =====
    
    /**
     * 获取打印机状态
     * 可能的值: standby, printing, paused, error等
     */
    String getPrinterState() const;
    
    /**
     * 获取打印进度 (0.0-1.0)
     */
    float getPrintProgress() const;
    
    /**
     * 获取当前打印文件名
     */
    String getFilename() const;
    
    /**
     * 获取喷嘴当前温度 (°C)
     */
    float getExtruderTemp() const;
    
    /**
     * 获取喷嘴目标温度 (°C)
     */
    float getExtruderTarget() const;
    
    /**
     * 获取热床当前温度 (°C)
     */
    float getBedTemp() const;
    
    /**
     * 获取热床目标温度 (°C)
     */
    float getBedTarget() const;

    /**
     * 获取宏变量状态
     *
     * @param variableName 变量名称
     * @return 变量值字符串，如果不存在返回空字符串
     */
    String getMacroVariable(const String& variableName) const;

private:
    // WebSocket客户端
    websockets::WebsocketsClient client;

    // 服务器信息
    String moonrakerHost;
    int moonrakerPort;

    // 打印状态
    String printerState;
    float printProgress;
    String filename;

    // 温度
    float extruderTemp;
    float extruderTarget;
    float bedTemp;
    float bedTarget;

    // 宏变量存储
    StaticJsonDocument<512> macroVariables;

    // 重连机制
    unsigned long lastReconnectAttempt;

    // 错误处理
    int reconnectAttempts;              // 重连尝试次数
    int jsonParseErrors;                // JSON解析错误次数
    unsigned long lastErrorReset;       // 上次错误重置时间

    // 状态变化检测（仅用于串口输出控制）
    String lastPrinterState;
    float lastPrintProgress;
    String lastFilename;
    float lastExtruderTemp;
    float lastExtruderTarget;
    float lastBedTemp;
    float lastBedTarget;

    // 静态JSON文档，避免频繁内存分配
    static StaticJsonDocument<JSON_MESSAGE_BUFFER_SIZE> messageDoc;     // 用于解析接收的消息
    static StaticJsonDocument<JSON_SEND_BUFFER_SIZE> sendDoc;           // 用于发送消息
    
    /**
     * WebSocket消息处理回调
     * 解析从Moonraker服务器接收的JSON消息
     */
    void onMessage(websockets::WebsocketsMessage message);
    
    /**
     * WebSocket事件处理回调
     * 处理连接、断开等事件
     */
    void onEvent(websockets::WebsocketsEvent event, String data);
    
    /**
     * 订阅Moonraker对象更新
     * 用于接收打印机状态、温度等实时更新
     */
    void subscribeToObjects();
    
    /**
     * 重置所有状态变量
     * 在Klipper重启或断开连接时调用
     */
    void resetAllStates();

    /**
     * 输出状态信息到串口（仅在状态变化时）
     */
    void printStatusIfChanged();

    /**
     * 处理错误并检查是否需要重置
     */
    void handleError(const String& errorType);

    /**
     * 重置错误计数器
     */
    void resetErrorCounters();
    
    // 静态回调函数，用于桥接到类实例
    static void staticOnMessage(websockets::WebsocketsClient& client, websockets::WebsocketsMessage message);
    static void staticOnEvent(websockets::WebsocketsClient& client, websockets::WebsocketsEvent event, String data);
    
    // 状态变化回调函数类型
    typedef std::function<void()> StatusChangeCallback;
    StatusChangeCallback statusChangeCallback;

    // 全局实例指针，用于静态回调
    static MoonrakerClient* instance;

public:
    /**
     * 设置状态变化回调函数
     * 当从Moonraker接收到状态更新时会调用此回调
     */
    void setStatusChangeCallback(StatusChangeCallback callback) {
        statusChangeCallback = callback;
    }
};
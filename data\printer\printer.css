/* 打印机模块专用样式 */

/* 温度显示样式 */
.temp-item {
    margin-bottom: 10px;
    padding: 8px 12px;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #007bff;
}

.temp-item:last-child {
    margin-bottom: 0;
}

/* 温度头部布局 */
.temp-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 4px;
}

.temp-label {
    font-size: 0.9rem;
    font-weight: 600;
    color: #666;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.temp-display {
    display: flex;
    align-items: baseline;
    gap: 3px;
}

.temp-current {
    font-size: 1.3rem;
    font-weight: 700;
    color: #333;
    transition: all 0.3s ease;
}

/* 加热时的动画效果 */
.temp-current.heating {
    animation: heating-glow 2s ease-in-out infinite;
    color: #ff6b35;
    text-shadow: 0 0 8px rgba(255, 107, 53, 0.6);
}

@keyframes heating-glow {
    0%, 100% {
        color: #ff6b35;
        text-shadow: 0 0 8px rgba(255, 107, 53, 0.6);
    }
    25% {
        color: #ff8e53;
        text-shadow: 0 0 12px rgba(255, 142, 83, 0.8);
    }
    50% {
        color: #ff4757;
        text-shadow: 0 0 16px rgba(255, 71, 87, 1);
    }
    75% {
        color: #ff6348;
        text-shadow: 0 0 12px rgba(255, 99, 72, 0.8);
    }
}

.temp-separator {
    font-size: 1rem;
    color: #666;
    margin: 0 1px;
}

.temp-target {
    font-size: 1rem;
    font-weight: 600;
    color: #666;
}

.temp-unit {
    font-size: 0.9rem;
    color: #999;
    margin-left: 3px;
}

/* 温度进度条 */
.temp-bar {
    width: 100%;
    height: 4px;
    background-color: #e9ecef;
    border-radius: 2px;
    overflow: hidden;
    position: relative;
    margin-top: 2px;
}

.temp-progress {
    height: 100%;
    border-radius: 2px;
    transition: width 0.5s ease;
    width: 0%;
}

/* 喷嘴温度进度条颜色 */
.temp-item:first-child .temp-progress {
    background: linear-gradient(90deg, #ff6b6b, #ff8e53);
}

/* 热床温度进度条颜色 */
.temp-item:last-child .temp-progress {
    background: linear-gradient(90deg, #4ecdc4, #44a08d);
}

/* 状态值颜色 */
.status-value.state-standby {
    color: #6c757d;
}

.status-value.state-printing {
    color: #28a745;
    font-weight: 600;
}

.status-value.state-paused {
    color: #ffc107;
    font-weight: 600;
}

.status-value.state-error {
    color: #dc3545;
    font-weight: 600;
}

.status-value.state-complete {
    color: #17a2b8;
    font-weight: 600;
}

/* 打印进度特殊样式 */
.progress-container.printing .progress-fill {
    background: linear-gradient(90deg, #28a745, #20c997);
    animation: progress-glow 2s ease-in-out infinite alternate;
}

@keyframes progress-glow {
    from {
        box-shadow: 0 0 5px rgba(40, 167, 69, 0.5);
    }
    to {
        box-shadow: 0 0 15px rgba(40, 167, 69, 0.8);
    }
}

/* 连接状态指示器动画 */
.status-indicator.connecting {
    animation: connecting-pulse 1.5s ease-in-out infinite;
}

@keyframes connecting-pulse {
    0%, 100% {
        opacity: 1;
        transform: scale(1);
    }
    50% {
        opacity: 0.7;
        transform: scale(1.1);
    }
}

/* 文件名显示优化 */
.status-value.filename {
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
    word-break: break-all;
    max-width: 200px;
}

/* 刷新按钮动画 */
.btn.refreshing {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* 温度警告状态 */
.temp-item.warning {
    border-left-color: #ffc107;
    background: #fff3cd;
}

.temp-item.warning .temp-current {
    color: #856404;
}

.temp-item.danger {
    border-left-color: #dc3545;
    background: #f8d7da;
}

.temp-item.danger .temp-current {
    color: #721c24;
}

/* 数据加载状态 */
.status-value.loading {
    color: #6c757d;
    font-style: italic;
}

.status-value.loading::after {
    content: "...";
    animation: loading-dots 1.5s infinite;
}

@keyframes loading-dots {
    0%, 20% { content: ""; }
    40% { content: "."; }
    60% { content: ".."; }
    80%, 100% { content: "..."; }
}

/* 响应式温度显示 */
@media (max-width: 768px) {
    .temp-display {
        flex-direction: column;
        align-items: flex-start;
        gap: 2px;
    }
    
    .temp-current {
        font-size: 1.5rem;
    }
    
    .temp-target {
        font-size: 1rem;
    }
    
    .status-value.filename {
        max-width: 150px;
        font-size: 0.8rem;
    }
}

@media (max-width: 480px) {
    .temp-item {
        padding: 10px;
        margin-bottom: 15px;
    }
    
    .temp-current {
        font-size: 1.3rem;
    }
    
    .temp-display {
        margin-bottom: 8px;
    }
}

/* ==================== 宏状态监控样式 ==================== */

/* 宏状态区域布局 */
.macro-status-section {
    margin-bottom: 8px;
    padding: 12px 20px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 宏状态网格布局 */
.macro-status-grid {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    align-items: center;
    justify-content: center;
}

/* 宏状态文本样式 */
.macro-status-text {
    font-size: 0.95rem;
    color: #495057;
    white-space: nowrap;
}

.macro-status-text .macro-name {
    font-weight: 500;
    color: #495057;
}

.macro-status-text .status-separator {
    color: #495057;
    margin: 0 2px;
}

/* 空闲状态 */
.macro-status-text.idle .status-text {
    color: #495057;
    font-weight: 400;
}

/* 运行中状态 */
.macro-status-text.running .status-text {
    color: #28a745;
    font-weight: 600;
    animation: text-glow 2s ease-in-out infinite;
}

/* 运行中状态动画 */
@keyframes text-glow {
    0%, 100% {
        color: #28a745;
        text-shadow: none;
    }
    50% {
        color: #20c997;
        text-shadow: 0 0 8px rgba(40, 167, 69, 0.4);
    }
}



/* 响应式设计 */
@media (max-width: 768px) {
    .macro-status-grid {
        gap: 16px;
    }

    .macro-status-text {
        font-size: 0.9rem;
    }
}

@media (max-width: 480px) {
    .macro-status-section {
        padding: 10px 15px;
    }

    .macro-status-grid {
        gap: 12px;
    }

    .macro-status-text {
        font-size: 0.85rem;
    }
}

#pragma once

#include <Arduino.h>
#include <ArduinoJson.h>
#include <ESPAsyncWebServer.h>
#include <LittleFS.h>
#include <vector>

/**
 * LED分段数据结构
 */
struct LEDSegment {
    String id;          // 分段唯一标识符
    String name;        // 分段名称
    int startLed;       // 起始灯珠位置（从1开始）
    int endLed;         // 结束灯珠位置（包含）
    
    // 构造函数
    LEDSegment() : startLed(1), endLed(1) {}
    LEDSegment(const String& segmentId, const String& segmentName, int start, int end)
        : id(segmentId), name(segmentName), startLed(start), endLed(end) {}
    
    // 获取分段包含的灯珠数量
    int getLength() const {
        return endLed - startLed + 1;
    }
    
    // 检查是否与另一个分段重叠
    bool overlapsWith(const LEDSegment& other) const {
        return !(endLed < other.startLed || startLed > other.endLed);
    }
    
    // 转换为JSON对象
    JsonObject toJson(JsonDocument& doc) const {
        JsonObject obj = doc.createNestedObject();
        obj["id"] = id;
        obj["name"] = name;
        obj["startLed"] = startLed;
        obj["endLed"] = endLed;
        obj["length"] = getLength();
        return obj;
    }
    
    // 从JSON对象创建分段
    static LEDSegment fromJson(const JsonObject& obj) {
        LEDSegment segment;
        segment.id = obj["id"].as<String>();
        segment.name = obj["name"].as<String>();
        segment.startLed = obj["startLed"].as<int>();
        segment.endLed = obj["endLed"].as<int>();
        return segment;
    }
};

/**
 * LED分段管理器
 * 
 * 负责LED灯带分段的创建、编辑、删除和持久化存储
 * 提供REST API接口供前端调用
 */
class SegmentsManager {
public:
    /**
     * 构造函数
     * 
     * @param totalLeds 总LED灯珠数量
     */
    SegmentsManager(int totalLeds = 37);
    
    /**
     * 析构函数
     */
    ~SegmentsManager();
    
    /**
     * 初始化分段管理器
     * 从文件系统加载已保存的分段配置
     */
    void begin();
    
    /**
     * 注册API端点到WebServer
     * 
     * @param server AsyncWebServer实例引用
     */
    void registerAPI(AsyncWebServer& server);
    
    /**
     * 获取所有分段
     * 
     * @return 分段列表的引用
     */
    const std::vector<LEDSegment>& getSegments() const;
    
    /**
     * 根据ID获取分段
     * 
     * @param id 分段ID
     * @return 分段指针，如果不存在返回nullptr
     */
    const LEDSegment* getSegmentById(const String& id) const;
    
    /**
     * 添加新分段
     * 
     * @param segment 要添加的分段
     * @return 成功返回true，失败返回false
     */
    bool addSegment(const LEDSegment& segment);
    
    /**
     * 更新分段
     * 
     * @param id 要更新的分段ID
     * @param segment 新的分段数据
     * @return 成功返回true，失败返回false
     */
    bool updateSegment(const String& id, const LEDSegment& segment);
    
    /**
     * 删除分段
     * 
     * @param id 要删除的分段ID
     * @return 成功返回true，失败返回false
     */
    bool deleteSegment(const String& id);
    
    /**
     * 获取总LED数量
     */
    int getTotalLeds() const;
    
    /**
     * 获取已使用的LED数量
     */
    int getUsedLeds() const;
    
    /**
     * 获取剩余可用的LED数量
     */
    int getAvailableLeds() const;
    
    /**
     * 获取下一个分段的建议起始位置
     */
    int getNextStartPosition() const;
    
    /**
     * 验证分段是否有效（不重叠、在范围内）
     * 
     * @param segment 要验证的分段
     * @param excludeId 验证时排除的分段ID（用于更新时）
     * @return 有效返回true，无效返回false
     */
    bool validateSegment(const LEDSegment& segment, const String& excludeId = "") const;
    
    /**
     * 生成唯一的分段ID
     */
    String generateSegmentId() const;

private:
    int totalLeds;                          // 总LED数量
    std::vector<LEDSegment> segments;       // 分段列表
    const String configFile = "/segments.json";  // 配置文件路径
    
    /**
     * 保存分段配置到文件
     */
    bool saveToFile();
    
    /**
     * 从文件加载分段配置
     */
    bool loadFromFile();
    
    /**
     * API处理函数
     */
    void handleGetSegments(AsyncWebServerRequest* request);
    void handleCreateSegment(AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total);
    void handleUpdateSegment(AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total);
    void handleDeleteSegment(AsyncWebServerRequest* request);
    void handleGetStats(AsyncWebServerRequest* request);
    
    /**
     * 工具函数
     */
    void sendJsonResponse(AsyncWebServerRequest* request, int code, const String& message, const JsonDocument* data = nullptr);
    void sendErrorResponse(AsyncWebServerRequest* request, int code, const String& message);
};

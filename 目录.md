/
├── src/                            # 后端源代码目录
│   ├── main.cpp                    # 主程序入口
│   ├── WiFiManager.cpp             # WiFi连接管理
│   ├── MoonrakerClient.cpp         # Moonraker客户端实现
│   ├── WebServer.cpp               # Web服务器实现
│   ├── PrinterManager.cpp          # 打印机状态实现
│   ├── LEDController.cpp           # LED控制器实现
│   ├── SegmentsManager.cpp         # 分段管理
│   ├── EffectsManager.cpp          # 灯效管理
│   ├── ScenesManager.cpp           # 场景管理
│   └── MacrosManager.cpp           # 宏命令管理
│
├── include/                        # 头文件目录
│   ├── Config.h                    # 全局配置
│   ├── WiFiManager.h               # WiFi管理器头文件
│   ├── MoonrakerClient.h           # Moonraker客户端头文件
│   ├── WebServer.h                 # Web服务器头文件
│   ├── PrinterManager.h            # 打印机状态头文件
│   ├── LEDController.h             # LED控制器头文件
│   ├── SegmentsManager.h           # 分段管理头文件
│   ├── EffectsManager.h            # 灯效管理头文件
│   ├── ScenesManager.h             # 场景管理头文件
│   └── MacrosManager.h             # 宏命令管理头文件
│
├── data/                           # 前端文件目录 (LittleFS)
│   ├── index.html                  # 主HTML文件
│   ├── style.css                   # 主样式文件
│   ├── script.js                   # 主JS文件
│   ├── printer/                    # 打印机状态模块
│   │   ├── printer.css             # 打印机状态模块样式
│   │   └── printer.js              # 打印机状态模块逻辑
│   ├── segments/                   # 分段预设模块
│   │   ├── segments.css            # 分段预设模块样式
│   │   └── segments.js             # 分段预设模块逻辑
│   ├── effects/                    # 灯效预设模块
│   │   ├── effects.css             # 灯效预设模块样式
│   │   └── effects.js              # 灯效预设模块逻辑
│   ├── scenes/                     # 场景预设模块
│   │   ├── scenes.css              # 场景预设模块样式
│   │   └── scenes.js               # 场景预设模块逻辑
│   └── macros/                     # 自定义宏模块
│       ├── macros.css              # 自定义宏模块样式
│       └── macros.js               # 自定义宏模块逻辑
│
├── platformio.ini                  # PlatformIO配置文件
└── .gitignore                      # Git忽略文件
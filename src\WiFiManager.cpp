#include "WiFiManager.h"

/**
 * 构造函数
 * 
 * @param ssid WiFi网络名称
 * @param password WiFi密码
 * @param checkInterval 检查WiFi连接状态的时间间隔（毫秒）
 */
WiFiManager::WiFiManager(const char* ssid, const char* password, unsigned long checkInterval) :
    wifiSsid(ssid),
    wifiPassword(password),
    checkInterval(checkInterval),
    lastCheckTime(0),
    connectStartTime(0),
    connected(false),
    wifiState(WIFI_IDLE) {
}

/**
 * 开始连接到WiFi网络（非阻塞）
 *
 * @return 连接是否立即成功（通常返回false，需要通过checkConnection()轮询状态）
 */
bool WiFiManager::connect() {
    if (wifiState == WIFI_CONNECTING || wifiState == WIFI_RECONNECTING) {
        // 已经在连接中，返回当前状态
        return updateConnectionState();
    }

    WiFi.mode(WIFI_STA);
    WiFi.begin(wifiSsid, wifiPassword);
    Serial.println("开始连接到 WiFi...");

    wifiState = WIFI_CONNECTING;
    connectStartTime = millis();
    connected = false;

    // 立即检查一次状态
    return updateConnectionState();
}

/**
 * 检查WiFi连接状态并在需要时重连（非阻塞）
 * 应在主循环中定期调用
 *
 * @return WiFi是否已连接
 */
bool WiFiManager::checkConnection() {
    unsigned long currentMillis = millis();

    // 如果正在连接中，更新连接状态
    if (wifiState == WIFI_CONNECTING || wifiState == WIFI_RECONNECTING) {
        return updateConnectionState();
    }

    // 定期检查WiFi状态
    if (currentMillis - lastCheckTime >= checkInterval) {
        lastCheckTime = currentMillis;

        if (WiFi.status() != WL_CONNECTED) {
            if (connected) {
                // WiFi连接丢失
                Serial.println("WiFi连接丢失，开始重新连接...");
                connected = false;
                wifiState = WIFI_RECONNECTING;
            }

            // 开始非阻塞重连
            if (wifiState == WIFI_IDLE) {
                wifiState = WIFI_RECONNECTING;
            }

            WiFi.disconnect();
            WiFi.begin(wifiSsid, wifiPassword);
            connectStartTime = currentMillis;

            Serial.print(".");  // 显示重连进度
        } else {
            // WiFi已连接，确保状态正确
            if (!connected) {
                connected = true;
                wifiState = WIFI_CONNECTED;
                Serial.println("");
                Serial.print("WiFi连接成功，IP地址: ");
                Serial.println(WiFi.localIP());
            }
        }
    }

    return connected;
}

/**
 * 获取当前WiFi连接状态
 *
 * @return WiFi是否已连接
 */
bool WiFiManager::isConnected() const {
    // 直接检查实际WiFi状态，确保状态同步
    bool actualStatus = (WiFi.status() == WL_CONNECTED);

    // 更新内部状态以保持同步（虽然这是const方法，但状态同步很重要）
    if (actualStatus != connected) {
        const_cast<WiFiManager*>(this)->connected = actualStatus;
    }

    return actualStatus;
}

/**
 * 获取IP地址
 * 
 * @return 当前分配的IP地址（如果已连接）
 */
IPAddress WiFiManager::getIP() const {
    return WiFi.localIP();
}

/**
 * 更新连接状态（内部方法）
 *
 * @return 当前是否已连接
 */
bool WiFiManager::updateConnectionState() {
    unsigned long currentMillis = millis();

    if (WiFi.status() == WL_CONNECTED) {
        // 连接成功
        if (!connected) {
            connected = true;
            wifiState = WIFI_CONNECTED;
            Serial.println("");
            Serial.print("WiFi连接成功，IP地址: ");
            Serial.println(WiFi.localIP());
        }
        return true;
    } else {
        // 检查是否超时
        unsigned long timeout = (wifiState == WIFI_CONNECTING) ? 15000 : 10000;  // 首次连接15秒，重连10秒

        if (currentMillis - connectStartTime > timeout) {
            // 连接超时
            connected = false;
            wifiState = WIFI_IDLE;
            Serial.println("");
            Serial.println("WiFi连接超时");
            return false;
        }

        // 还在连接中
        return false;
    }
}